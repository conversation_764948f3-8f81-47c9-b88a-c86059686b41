<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_class') {
        try {
            $class_name = trim($_POST['class_name']);
            $section = trim($_POST['section']);
            $academic_year = trim($_POST['academic_year']);
            $class_teacher_id = !empty($_POST['class_teacher_id']) ? (int)$_POST['class_teacher_id'] : null;
            $status = $_POST['status'] ?? 'active';
            
            // Validate required fields
            if (empty($class_name) || empty($academic_year)) {
                throw new Exception('ক্লাসের নাম এবং শিক্ষাবর্ষ আবশ্যক।');
            }
            
            // Check if class already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM classes WHERE class_name = ? AND section = ? AND academic_year = ?");
            $stmt->execute([$class_name, $section, $academic_year]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('এই ক্লাস ইতিমধ্যে বিদ্যমান।');
            }
            
            // Insert new class
            $stmt = $pdo->prepare("
                INSERT INTO classes (class_name, section, class_teacher_id, academic_year, status)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$class_name, $section, $class_teacher_id, $academic_year, $status]);
            
            $message = 'নতুন ক্লাস সফলভাবে যোগ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'update_class') {
        try {
            $id = (int)$_POST['id'];
            $class_name = trim($_POST['class_name']);
            $section = trim($_POST['section']);
            $academic_year = trim($_POST['academic_year']);
            $class_teacher_id = !empty($_POST['class_teacher_id']) ? (int)$_POST['class_teacher_id'] : null;
            $status = $_POST['status'];
            
            // Validate required fields
            if (empty($class_name) || empty($academic_year)) {
                throw new Exception('ক্লাসের নাম এবং শিক্ষাবর্ষ আবশ্যক।');
            }
            
            // Check if class already exists (excluding current)
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM classes WHERE class_name = ? AND section = ? AND academic_year = ? AND id != ?");
            $stmt->execute([$class_name, $section, $academic_year, $id]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('এই ক্লাস ইতিমধ্যে বিদ্যমান।');
            }
            
            // Update class
            $stmt = $pdo->prepare("
                UPDATE classes 
                SET class_name = ?, section = ?, class_teacher_id = ?, academic_year = ?, status = ?
                WHERE id = ?
            ");
            $stmt->execute([$class_name, $section, $class_teacher_id, $academic_year, $status, $id]);
            
            $message = 'ক্লাসের তথ্য সফলভাবে আপডেট করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'delete_class') {
        try {
            $id = (int)$_POST['id'];
            
            // Check if class has students
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM students WHERE class_id = ?");
            $stmt->execute([$id]);
            $student_count = $stmt->fetchColumn();
            
            if ($student_count > 0) {
                throw new Exception("এই ক্লাসে {$student_count} জন ছাত্র আছে। প্রথমে ছাত্রদের অন্য ক্লাসে স্থানান্তর করুন।");
            }
            
            // Delete class
            $stmt = $pdo->prepare("DELETE FROM classes WHERE id = ?");
            $stmt->execute([$id]);
            
            $message = 'ক্লাস সফলভাবে মুছে ফেলা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get search parameters
$search_name = $_GET['search_name'] ?? '';
$search_year = $_GET['search_year'] ?? '';
$search_status = $_GET['search_status'] ?? '';

// Build search query
$where_conditions = [];
$params = [];

if (!empty($search_name)) {
    $where_conditions[] = "(c.class_name LIKE ? OR c.section LIKE ?)";
    $params[] = "%{$search_name}%";
    $params[] = "%{$search_name}%";
}

if (!empty($search_year)) {
    $where_conditions[] = "c.academic_year = ?";
    $params[] = $search_year;
}

if (!empty($search_status)) {
    $where_conditions[] = "c.status = ?";
    $params[] = $search_status;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get classes with teacher info and student count
try {
    $stmt = $pdo->prepare("
        SELECT c.*, 
               CONCAT(t.first_name, ' ', t.last_name) as teacher_name,
               COUNT(s.id) as student_count
        FROM classes c
        LEFT JOIN teachers t ON c.class_teacher_id = t.id
        LEFT JOIN students s ON c.id = s.class_id
        {$where_clause}
        GROUP BY c.id
        ORDER BY c.class_name, c.section
    ");
    $stmt->execute($params);
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $classes = [];
    $error = 'ডেটা লোড করতে সমস্যা হয়েছে।';
}

// Get teachers for dropdown
try {
    $stmt = $pdo->query("SELECT id, CONCAT(first_name, ' ', last_name) as name FROM teachers WHERE status = 'active' ORDER BY first_name");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $teachers = [];
}

// Get academic years for dropdown
try {
    $stmt = $pdo->query("SELECT DISTINCT academic_year FROM classes ORDER BY academic_year DESC");
    $academic_years = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $academic_years = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্লাস ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .classes-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .search-section {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .add-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .add-btn:hover {
            transform: translateY(-2px);
        }
        
        .classes-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .classes-table th,
        .classes-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .classes-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
        }
        
        .classes-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-edit {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .modal-title {
            color: #2c3e50;
            font-size: 1.5rem;
            margin: 0;
        }
        
        .close {
            color: #aaa;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            border: none;
            background: none;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .classes-table {
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .search-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">🏫 ক্লাস ব্যবস্থাপনা</h1>
                <p class="content-subtitle">ক্লাস তৈরি, সম্পাদনা ও ব্যবস্থাপনা</p>
            </div>

            <div class="content-body">
                <div class="classes-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count($classes); ?></div>
                            <div class="stat-label">মোট ক্লাস</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count(array_filter($classes, fn($c) => $c['status'] == 'active')); ?></div>
                            <div class="stat-label">সক্রিয় ক্লাস</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo array_sum(array_column($classes, 'student_count')); ?></div>
                            <div class="stat-label">মোট ছাত্র</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count($academic_years); ?></div>
                            <div class="stat-label">শিক্ষাবর্ষ</div>
                        </div>
                    </div>

                    <!-- Add New Class Button -->
                    <button class="add-btn" onclick="openAddModal()">
                        ➕ নতুন ক্লাস যোগ করুন
                    </button>

                    <!-- Search Section -->
                    <div class="search-section">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">🔍 ক্লাস অনুসন্ধান</h3>

                        <form method="GET" class="search-grid">
                            <div class="form-group">
                                <label class="form-label">ক্লাসের নাম/সেকশন</label>
                                <input type="text" name="search_name" class="form-control"
                                       value="<?php echo htmlspecialchars($search_name); ?>"
                                       placeholder="ক্লাসের নাম বা সেকশন লিখুন">
                            </div>

                            <div class="form-group">
                                <label class="form-label">শিক্ষাবর্ষ</label>
                                <select name="search_year" class="form-control">
                                    <option value="">সব শিক্ষাবর্ষ</option>
                                    <?php foreach ($academic_years as $year): ?>
                                        <option value="<?php echo htmlspecialchars($year); ?>"
                                                <?php echo ($search_year == $year) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($year); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">অবস্থা</label>
                                <select name="search_status" class="form-control">
                                    <option value="">সব অবস্থা</option>
                                    <option value="active" <?php echo ($search_status == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                    <option value="inactive" <?php echo ($search_status == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="submit-btn" style="margin-top: 0; padding: 0.75rem 1.5rem;">
                                    🔍 অনুসন্ধান
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Classes Table -->
                    <div style="overflow-x: auto;">
                        <table class="classes-table">
                            <thead>
                                <tr>
                                    <th>ক্লাসের নাম</th>
                                    <th>সেকশন</th>
                                    <th>ক্লাস শিক্ষক</th>
                                    <th>শিক্ষাবর্ষ</th>
                                    <th>ছাত্র সংখ্যা</th>
                                    <th>অবস্থা</th>
                                    <th>কার্যক্রম</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($classes)): ?>
                                    <tr>
                                        <td colspan="7" style="text-align: center; padding: 3rem; color: #6c757d;">
                                            <h4>📚 কোন ক্লাস পাওয়া যায়নি</h4>
                                            <p>নতুন ক্লাস যোগ করুন অথবা অনুসন্ধান ফিল্টার পরিবর্তন করুন।</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($classes as $class): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($class['class_name'] ?? ''); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($class['section'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($class['teacher_name'] ?? 'নির্ধারিত নয়'); ?></td>
                                            <td><?php echo htmlspecialchars($class['academic_year'] ?? ''); ?></td>
                                            <td>
                                                <span style="background: #e3f2fd; padding: 0.25rem 0.75rem; border-radius: 15px; color: #1976d2;">
                                                    <?php echo $class['student_count'] ?? 0; ?> জন
                                                </span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $class['status'] ?? 'active'; ?>">
                                                    <?php echo ($class['status'] ?? 'active') == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn-edit" onclick="openEditModal(<?php echo htmlspecialchars(json_encode($class)); ?>)">
                                                        ✏️ সম্পাদনা
                                                    </button>
                                                    <button class="btn-delete" onclick="deleteClass(<?php echo $class['id']; ?>, '<?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>')">
                                                        🗑️ মুছুন
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Class Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">➕ নতুন ক্লাস যোগ করুন</h2>
                <button class="close" onclick="closeModal('addModal')">&times;</button>
            </div>

            <form method="POST" id="addForm">
                <input type="hidden" name="action" value="add_class">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">ক্লাসের নাম *</label>
                        <input type="text" name="class_name" class="form-control" required
                               placeholder="যেমন: Class 1, Class 2">
                    </div>

                    <div class="form-group">
                        <label class="form-label">সেকশন</label>
                        <input type="text" name="section" class="form-control"
                               placeholder="যেমন: A, B, C">
                    </div>

                    <div class="form-group">
                        <label class="form-label">শিক্ষাবর্ষ *</label>
                        <input type="text" name="academic_year" class="form-control" required
                               value="<?php echo date('Y'); ?>" placeholder="যেমন: 2024">
                    </div>

                    <div class="form-group">
                        <label class="form-label">ক্লাস শিক্ষক</label>
                        <select name="class_teacher_id" class="form-control">
                            <option value="">শিক্ষক নির্বাচন করুন</option>
                            <?php foreach ($teachers as $teacher): ?>
                                <option value="<?php echo $teacher['id']; ?>">
                                    <?php echo htmlspecialchars($teacher['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">অবস্থা</label>
                        <select name="status" class="form-control">
                            <option value="active">সক্রিয়</option>
                            <option value="inactive">নিষ্ক্রিয়</option>
                        </select>
                    </div>
                </div>

                <button type="submit" class="submit-btn">
                    💾 ক্লাস সংরক্ষণ করুন
                </button>
            </form>
        </div>
    </div>

    <!-- Edit Class Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">✏️ ক্লাস সম্পাদনা করুন</h2>
                <button class="close" onclick="closeModal('editModal')">&times;</button>
            </div>

            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_class">
                <input type="hidden" name="id" id="edit_id">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">ক্লাসের নাম *</label>
                        <input type="text" name="class_name" id="edit_class_name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">সেকশন</label>
                        <input type="text" name="section" id="edit_section" class="form-control">
                    </div>

                    <div class="form-group">
                        <label class="form-label">শিক্ষাবর্ষ *</label>
                        <input type="text" name="academic_year" id="edit_academic_year" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">ক্লাস শিক্ষক</label>
                        <select name="class_teacher_id" id="edit_class_teacher_id" class="form-control">
                            <option value="">শিক্ষক নির্বাচন করুন</option>
                            <?php foreach ($teachers as $teacher): ?>
                                <option value="<?php echo $teacher['id']; ?>">
                                    <?php echo htmlspecialchars($teacher['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">অবস্থা</label>
                        <select name="status" id="edit_status" class="form-control">
                            <option value="active">সক্রিয়</option>
                            <option value="inactive">নিষ্ক্রিয়</option>
                        </select>
                    </div>
                </div>

                <button type="submit" class="submit-btn">
                    💾 পরিবর্তন সংরক্ষণ করুন
                </button>
            </form>
        </div>
    </div>

    <!-- Delete Form -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_class">
        <input type="hidden" name="id" id="delete_id">
    </form>

    <script>
        // Modal functions
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        function openEditModal(classData) {
            document.getElementById('edit_id').value = classData.id || '';
            document.getElementById('edit_class_name').value = classData.class_name || '';
            document.getElementById('edit_section').value = classData.section || '';
            document.getElementById('edit_academic_year').value = classData.academic_year || '';
            document.getElementById('edit_class_teacher_id').value = classData.class_teacher_id || '';
            document.getElementById('edit_status').value = classData.status || 'active';

            document.getElementById('editModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function deleteClass(id, className) {
            if (confirm(`আপনি কি নিশ্চিত যে "${className}" ক্লাসটি মুছে ফেলতে চান?\n\nসতর্কতা: এই ক্রিয়াটি পূর্বাবস্থায় ফেরানো যাবে না।`)) {
                document.getElementById('delete_id').value = id;
                document.getElementById('deleteForm').submit();
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Form validation
        document.getElementById('addForm').addEventListener('submit', function(e) {
            const className = this.querySelector('[name="class_name"]').value.trim();
            const academicYear = this.querySelector('[name="academic_year"]').value.trim();

            if (!className || !academicYear) {
                e.preventDefault();
                alert('ক্লাসের নাম এবং শিক্ষাবর্ষ আবশ্যক।');
                return false;
            }
        });

        document.getElementById('editForm').addEventListener('submit', function(e) {
            const className = this.querySelector('[name="class_name"]').value.trim();
            const academicYear = this.querySelector('[name="academic_year"]').value.trim();

            if (!className || !academicYear) {
                e.preventDefault();
                alert('ক্লাসের নাম এবং শিক্ষাবর্ষ আবশ্যক।');
                return false;
            }
        });

        // Auto-focus on modal open
        document.addEventListener('DOMContentLoaded', function() {
            const addModal = document.getElementById('addModal');
            const editModal = document.getElementById('editModal');

            // Focus first input when modal opens
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const modal = mutation.target;
                        if (modal.style.display === 'block') {
                            const firstInput = modal.querySelector('input[type="text"]');
                            if (firstInput) {
                                setTimeout(() => firstInput.focus(), 100);
                            }
                        }
                    }
                });
            });

            observer.observe(addModal, { attributes: true });
            observer.observe(editModal, { attributes: true });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        openAddModal();
                        break;
                }
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal[style*="block"]');
                if (openModal) {
                    openModal.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
