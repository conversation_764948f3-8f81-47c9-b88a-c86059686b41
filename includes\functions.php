<?php
/**
 * Common Functions for School Management System
 * This file contains utility functions used throughout the application
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * Redirect to login page if not logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ../login.php');
        exit();
    }
}

/**
 * Sanitize input data
 * @param string $data
 * @return string
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Generate a random student ID
 * @return string
 */
function generateStudentId() {
    return 'STU' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * Generate a random teacher ID
 * @return string
 */
function generateTeacherId() {
    return 'TCH' . date('Y') . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
}

/**
 * Format date for display
 * @param string $date
 * @param string $format
 * @return string
 */
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date) || $date == '0000-00-00') {
        return 'N/A';
    }
    return date($format, strtotime($date));
}

/**
 * Format currency for display
 * @param float $amount
 * @return string
 */
function formatCurrency($amount) {
    return '৳ ' . number_format($amount, 2);
}

/**
 * Get current academic year
 * @return string
 */
function getCurrentAcademicYear() {
    $currentMonth = date('n');
    $currentYear = date('Y');
    
    // Academic year starts from January
    if ($currentMonth >= 1) {
        return $currentYear;
    } else {
        return ($currentYear - 1);
    }
}

/**
 * Get list of classes
 * @param PDO $pdo
 * @return array
 */
function getClasses($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM classes ORDER BY class_name");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Get list of subjects
 * @param PDO $pdo
 * @return array
 */
function getSubjects($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM subjects ORDER BY subject_name");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Get student by ID
 * @param PDO $pdo
 * @param int $student_id
 * @return array|false
 */
function getStudentById($pdo, $student_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT s.*, c.class_name 
            FROM students s 
            LEFT JOIN classes c ON s.class_id = c.id 
            WHERE s.id = ?
        ");
        $stmt->execute([$student_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Get teacher by ID
 * @param PDO $pdo
 * @param int $teacher_id
 * @return array|false
 */
function getTeacherById($pdo, $teacher_id) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM teachers WHERE id = ?");
        $stmt->execute([$teacher_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Log activity
 * @param PDO $pdo
 * @param string $action
 * @param string $description
 * @param int $user_id
 */
function logActivity($pdo, $action, $description, $user_id = null) {
    try {
        if ($user_id === null && isset($_SESSION['user_id'])) {
            $user_id = $_SESSION['user_id'];
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, description, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$user_id, $action, $description]);
    } catch (PDOException $e) {
        // Log error silently
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

/**
 * Send notification
 * @param PDO $pdo
 * @param int $user_id
 * @param string $title
 * @param string $message
 * @param string $type
 */
function sendNotification($pdo, $user_id, $title, $message, $type = 'info') {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO notifications (user_id, title, message, type, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$user_id, $title, $message, $type]);
    } catch (PDOException $e) {
        error_log("Failed to send notification: " . $e->getMessage());
    }
}

/**
 * Get unread notifications count
 * @param PDO $pdo
 * @param int $user_id
 * @return int
 */
function getUnreadNotificationsCount($pdo, $user_id) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM notifications 
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$user_id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    } catch (PDOException $e) {
        return 0;
    }
}

/**
 * Validate email address
 * @param string $email
 * @return bool
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Bangladesh format)
 * @param string $phone
 * @return bool
 */
function isValidPhone($phone) {
    // Remove spaces and dashes
    $phone = preg_replace('/[\s\-]/', '', $phone);
    
    // Check if it's a valid Bangladesh phone number
    return preg_match('/^(\+88)?01[3-9]\d{8}$/', $phone);
}



/**
 * Generate CSRF token
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * @param string $token
 * @return bool
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Upload file with validation
 * @param array $file
 * @param string $uploadDir
 * @param array $allowedTypes
 * @param int $maxSize
 * @return array
 */
function uploadFile($file, $uploadDir, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'], $maxSize = 2097152) {
    $result = ['success' => false, 'message' => '', 'filename' => ''];
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $result['message'] = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
        return $result;
    }
    
    if ($file['size'] > $maxSize) {
        $result['message'] = 'ফাইলের সাইজ অনেক বড়। সর্বোচ্চ ' . ($maxSize / 1024 / 1024) . 'MB হতে পারে।';
        return $result;
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        $result['message'] = 'এই ধরনের ফাইল সাপোর্ট করা হয় না।';
        return $result;
    }
    
    $filename = uniqid() . '.' . $fileExtension;
    $uploadPath = $uploadDir . '/' . $filename;
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        $result['success'] = true;
        $result['filename'] = $filename;
        $result['message'] = 'ফাইল সফলভাবে আপলোড হয়েছে।';
    } else {
        $result['message'] = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    }
    
    return $result;
}

/**
 * Get current page name
 * @return string
 */
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF']);
}

/**
 * Check if current page is active
 * @param string $page
 * @return bool
 */
function isActivePage($page) {
    return getCurrentPage() === $page;
}

/**
 * Display success message
 * @param string $message
 */
function showSuccessMessage($message) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">';
    echo htmlspecialchars($message);
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}

/**
 * Display error message
 * @param string $message
 */
function showErrorMessage($message) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
    echo htmlspecialchars($message);
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}

/**
 * Display info message
 * @param string $message
 */
function showInfoMessage($message) {
    echo '<div class="alert alert-info alert-dismissible fade show" role="alert">';
    echo htmlspecialchars($message);
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}
?>
