<?php
require_once '../config/database.php';
checkRole('admin');

$pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USERNAME, DB_PASSWORD);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$message = '';
$error = '';

// Create fee_mapping table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS fee_mapping (
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_id INT NOT NULL,
            fee_category_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            is_mandatory BOOLEAN DEFAULT TRUE,
            academic_year VARCHAR(10) DEFAULT '2024-25',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
            FOREIGN KEY (fee_category_id) REFERENCES fee_categories(id) ON DELETE CASCADE,
            UNIQUE KEY unique_class_fee (class_id, fee_category_id, academic_year)
        )
    ");
} catch(PDOException $e) {
    // Table might already exist
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'];
        
        if ($action === 'save_mapping') {
            $class_id = (int)$_POST['class_id'];
            $fee_mappings = $_POST['fee_mappings'] ?? [];
            $academic_year = $_POST['academic_year'] ?? '2024-25';
            
            if (empty($class_id)) {
                throw new Exception('ক্লাস নির্বাচন করুন।');
            }
            
            // Delete existing mappings for this class and academic year
            $stmt = $pdo->prepare("DELETE FROM fee_mapping WHERE class_id = ? AND academic_year = ?");
            $stmt->execute([$class_id, $academic_year]);
            
            // Insert new mappings
            $insert_stmt = $pdo->prepare("
                INSERT INTO fee_mapping (class_id, fee_category_id, amount, is_mandatory, academic_year) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($fee_mappings as $fee_id => $fee_data) {
                if (!empty($fee_data['amount']) && $fee_data['amount'] > 0) {
                    $is_mandatory = isset($fee_data['mandatory']) ? 1 : 0;
                    $insert_stmt->execute([
                        $class_id, 
                        $fee_id, 
                        $fee_data['amount'], 
                        $is_mandatory, 
                        $academic_year
                    ]);
                }
            }
            
            $message = 'ফি ম্যাপিং সফলভাবে সংরক্ষণ করা হয়েছে!';
            
        } elseif ($action === 'bulk_copy') {
            $source_class = (int)$_POST['source_class'];
            $target_classes = $_POST['target_classes'] ?? [];
            $academic_year = $_POST['academic_year'] ?? '2024-25';

            if (empty($source_class) || empty($target_classes)) {
                throw new Exception('সোর্স ক্লাস এবং টার্গেট ক্লাস নির্বাচন করুন।');
            }

            // Get source class fee mappings
            $source_mappings = $pdo->prepare("
                SELECT fee_category_id, amount, is_mandatory
                FROM fee_mapping
                WHERE class_id = ? AND academic_year = ?
            ");
            $source_mappings->execute([$source_class, $academic_year]);
            $mappings = $source_mappings->fetchAll();

            if (empty($mappings)) {
                throw new Exception('সোর্স ক্লাসে কোনো ফি ম্যাপিং পাওয়া যায়নি।');
            }

            $insert_stmt = $pdo->prepare("
                INSERT IGNORE INTO fee_mapping (class_id, fee_category_id, amount, is_mandatory, academic_year)
                VALUES (?, ?, ?, ?, ?)
            ");

            foreach ($target_classes as $target_class) {
                foreach ($mappings as $mapping) {
                    $insert_stmt->execute([
                        $target_class,
                        $mapping['fee_category_id'],
                        $mapping['amount'],
                        $mapping['is_mandatory'],
                        $academic_year
                    ]);
                }
            }

            $message = 'ফি ম্যাপিং সফলভাবে কপি করা হয়েছে!';

        } elseif ($action === 'add_sub_category') {
            $parent_id = (int)$_POST['parent_id'];
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);

            if (empty($parent_id) || empty($category_name)) {
                throw new Exception('প্যারেন্ট ক্যাটেগরি এবং ক্যাটেগরির নাম প্রয়োজন।');
            }

            // Check if parent category exists
            $parent_check = $pdo->prepare("SELECT id FROM fee_categories WHERE id = ?");
            $parent_check->execute([$parent_id]);
            if (!$parent_check->fetch()) {
                throw new Exception('প্যারেন্ট ক্যাটেগরি পাওয়া যায়নি।');
            }

            // Insert new sub category
            $insert_stmt = $pdo->prepare("
                INSERT INTO fee_categories (category_name, description, category_type, parent_id)
                VALUES (?, ?, 'sub', ?)
            ");
            $insert_stmt->execute([$category_name, $description, $parent_id]);

            $message = 'সাব ক্যাটেগরি সফলভাবে যোগ করা হয়েছে!';

        } elseif ($action === 'edit_category') {
            $category_id = (int)$_POST['category_id'];
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);

            if (empty($category_id) || empty($category_name)) {
                throw new Exception('ক্যাটেগরি ID এবং নাম প্রয়োজন।');
            }

            // Update category
            $update_stmt = $pdo->prepare("
                UPDATE fee_categories
                SET category_name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $update_stmt->execute([$category_name, $description, $category_id]);

            $message = 'ক্যাটেগরি সফলভাবে আপডেট করা হয়েছে!';

        } elseif ($action === 'delete_category') {
            $category_id = (int)$_POST['category_id'];

            if (empty($category_id)) {
                throw new Exception('ক্যাটেগরি ID প্রয়োজন।');
            }

            // Check if category has sub-categories
            $sub_check = $pdo->prepare("SELECT COUNT(*) as count FROM fee_categories WHERE parent_id = ?");
            $sub_check->execute([$category_id]);
            $sub_count = $sub_check->fetch()['count'];

            if ($sub_count > 0) {
                throw new Exception('এই ক্যাটেগরির অধীনে সাব ক্যাটেগরি আছে। প্রথমে সাব ক্যাটেগরিগুলো মুছুন।');
            }

            // Check if category is used in fee mapping
            $mapping_check = $pdo->prepare("SELECT COUNT(*) as count FROM fee_mapping WHERE fee_category_id = ?");
            $mapping_check->execute([$category_id]);
            $mapping_count = $mapping_check->fetch()['count'];

            if ($mapping_count > 0) {
                throw new Exception('এই ক্যাটেগরি ফি ম্যাপিংয়ে ব্যবহৃত হচ্ছে। প্রথমে ম্যাপিং মুছুন।');
            }

            // Delete category
            $delete_stmt = $pdo->prepare("DELETE FROM fee_categories WHERE id = ?");
            $delete_stmt->execute([$category_id]);

            $message = 'ক্যাটেগরি সফলভাবে মুছে ফেলা হয়েছে!';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get classes
$classes = $pdo->query("SELECT * FROM classes ORDER BY class_name")->fetchAll();

// Get fee categories with hierarchy
$fee_categories = $pdo->query("
    SELECT 
        fc.*,
        parent.category_name as parent_name
    FROM fee_categories fc
    LEFT JOIN fee_categories parent ON fc.parent_id = parent.id
    ORDER BY 
        CASE WHEN fc.category_type = 'main' THEN 0 ELSE 1 END,
        COALESCE(fc.parent_id, fc.id),
        fc.category_name
")->fetchAll();

// Get current mappings for selected class
$selected_class = $_GET['class_id'] ?? ($classes[0]['id'] ?? null);
$selected_year = $_GET['academic_year'] ?? '2024-25';
$current_mappings = [];

if ($selected_class) {
    $stmt = $pdo->prepare("
        SELECT fm.*, fc.category_name, fc.category_type, fc.parent_id
        FROM fee_mapping fm
        JOIN fee_categories fc ON fm.fee_category_id = fc.id
        WHERE fm.class_id = ? AND fm.academic_year = ?
    ");
    $stmt->execute([$selected_class, $selected_year]);
    $mappings = $stmt->fetchAll();
    
    foreach ($mappings as $mapping) {
        $current_mappings[$mapping['fee_category_id']] = $mapping;
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ম্যাপিং - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .mapping-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .fee-category-group {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1rem;
            overflow: hidden;
        }
        
        .main-category-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .sub-category-item {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 1rem;
            align-items: center;
        }
        
        .sub-category-item:last-child {
            border-bottom: none;
        }
        
        .sub-category-item:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .category-name {
            font-weight: 500;
            color: #495057;
        }
        
        .amount-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .mandatory-checkbox {
            transform: scale(1.2);
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .bulk-copy-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            text-align: center;
        }
        
        .stat-item h3 {
            margin: 0;
            font-size: 2rem;
        }
        
        .stat-item p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-title {
            margin: 0;
            color: #495057;
            font-size: 1.5rem;
        }

        .close {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #6c757d;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close:hover {
            background-color: #f8f9fa;
            color: #495057;
        }

        .btn-add-sub:hover {
            background: rgba(255,255,255,0.3) !important;
            transform: translateY(-1px);
        }

        /* Action Buttons */
        .btn-action {
            background: none;
            border: 1px solid;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 30px;
            height: 30px;
        }

        .btn-edit {
            color: #007bff;
            border-color: #007bff;
        }

        .btn-edit:hover {
            background-color: #007bff;
            color: white;
        }

        .btn-delete {
            color: #dc3545;
            border-color: #dc3545;
        }

        .btn-delete:hover {
            background-color: #dc3545;
            color: white;
        }

        .category-name {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            width: 100%;
        }

        .main-category-item .category-name {
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="mapping-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div style="margin-bottom: 1rem;">
                    <a href="dashboard.php" class="btn btn-primary" style="background: rgba(255,255,255,0.2); border: 2px solid white;">
                        <i class="fas fa-arrow-left"></i> ড্যাশবোর্ডে ফিরুন
                    </a>
                </div>
                <h1><i class="fas fa-sitemap"></i> ফি ম্যাপিং সিস্টেম</h1>
                <p>বিভিন্ন ক্লাসের জন্য ফি ক্যাটেগরি ও পরিমাণ নির্ধারণ করুন</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Class Selection -->
            <div class="card">
                <h2><i class="fas fa-filter"></i> ক্লাস ও শিক্ষাবর্ষ নির্বাচন</h2>
                <form method="GET" style="margin-top: 1rem;">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">ক্লাস নির্বাচন করুন</label>
                            <select name="class_id" class="form-control" onchange="this.form.submit()">
                                <option value="">-- ক্লাস নির্বাচন করুন --</option>
                                <?php foreach ($classes as $class): ?>
                                    <option value="<?php echo $class['id']; ?>" 
                                            <?php echo $selected_class == $class['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">শিক্ষাবর্ষ</label>
                            <select name="academic_year" class="form-control" onchange="this.form.submit()">
                                <option value="2024-25" <?php echo $selected_year == '2024-25' ? 'selected' : ''; ?>>২০২৪-২৫</option>
                                <option value="2025-26" <?php echo $selected_year == '2025-26' ? 'selected' : ''; ?>>২০২৫-২৬</option>
                                <option value="2023-24" <?php echo $selected_year == '2023-24' ? 'selected' : ''; ?>>২০২৩-২৪</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <?php if ($selected_class): ?>
                <!-- Summary Card -->
                <?php 
                $total_amount = 0;
                $mandatory_count = 0;
                $optional_count = 0;
                foreach ($current_mappings as $mapping) {
                    $total_amount += $mapping['amount'];
                    if ($mapping['is_mandatory']) {
                        $mandatory_count++;
                    } else {
                        $optional_count++;
                    }
                }
                ?>
                <div class="summary-card">
                    <h2 style="margin: 0 0 1rem 0; text-align: center;">
                        <i class="fas fa-chart-pie"></i> ফি সারসংক্ষেপ
                    </h2>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <h3><?php echo number_format($total_amount, 2); ?> ৳</h3>
                            <p>মোট ফি</p>
                        </div>
                        <div class="stat-item">
                            <h3><?php echo $mandatory_count; ?></h3>
                            <p>বাধ্যতামূলক ফি</p>
                        </div>
                        <div class="stat-item">
                            <h3><?php echo $optional_count; ?></h3>
                            <p>ঐচ্ছিক ফি</p>
                        </div>
                        <div class="stat-item">
                            <h3><?php echo count($current_mappings); ?></h3>
                            <p>মোট ক্যাটেগরি</p>
                        </div>
                    </div>
                </div>

                <!-- Fee Mapping Form -->
                <div class="card">
                    <h2><i class="fas fa-cogs"></i> ফি ম্যাপিং কনফিগারেশন</h2>
                    
                    <form method="POST" style="margin-top: 2rem;">
                        <input type="hidden" name="action" value="save_mapping">
                        <input type="hidden" name="class_id" value="<?php echo $selected_class; ?>">
                        <input type="hidden" name="academic_year" value="<?php echo $selected_year; ?>">
                        
                        <?php 
                        $grouped_categories = [];
                        foreach ($fee_categories as $category) {
                            if ($category['category_type'] === 'main') {
                                $grouped_categories[$category['id']] = [
                                    'main' => $category,
                                    'subs' => []
                                ];
                            }
                        }
                        
                        foreach ($fee_categories as $category) {
                            if ($category['category_type'] === 'sub' && $category['parent_id']) {
                                if (isset($grouped_categories[$category['parent_id']])) {
                                    $grouped_categories[$category['parent_id']]['subs'][] = $category;
                                }
                            }
                        }
                        ?>
                        
                        <?php foreach ($grouped_categories as $group): ?>
                            <div class="fee-category-group">
                                <div class="main-category-header">
                                    <div style="display: flex; justify-content: between; align-items: center; width: 100%;">
                                        <div>
                                            <i class="fas fa-folder"></i> <?php echo htmlspecialchars($group['main']['category_name']); ?>
                                            <?php if ($group['main']['description']): ?>
                                                <small style="opacity: 0.8; margin-left: 1rem;">
                                                    (<?php echo htmlspecialchars($group['main']['description']); ?>)
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                        <button type="button" class="btn-add-sub" onclick="showAddSubCategoryModal(<?php echo $group['main']['id']; ?>, '<?php echo htmlspecialchars($group['main']['category_name']); ?>')" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer; font-size: 0.875rem;">
                                            <i class="fas fa-plus"></i> সাব ক্যাটেগরি যোগ করুন
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Main Category -->
                                <div class="sub-category-item main-category-item">
                                    <div class="category-name">
                                        <div>
                                            <i class="fas fa-tag"></i> <?php echo htmlspecialchars($group['main']['category_name']); ?>
                                            <small style="color: #6c757d;">(মূল ক্যাটেগরি)</small>
                                        </div>
                                        <div class="category-actions" style="display: flex; gap: 0.5rem;">
                                            <button type="button" class="btn-action btn-edit" onclick="editCategory(<?php echo $group['main']['id']; ?>, '<?php echo htmlspecialchars($group['main']['category_name']); ?>', '<?php echo htmlspecialchars($group['main']['description']); ?>')" title="সম্পাদনা">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn-action btn-delete" onclick="deleteCategory(<?php echo $group['main']['id']; ?>, '<?php echo htmlspecialchars($group['main']['category_name']); ?>')" title="মুছে ফেলুন">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                        <div class="main-category-total" style="font-weight: bold; color: #28a745; text-align: center; padding: 0.5rem; background: rgba(40, 167, 69, 0.1); border-radius: 5px;">
                                            মোট: <span id="main_total_<?php echo $group['main']['id']; ?>">০.০০ ৳</span>
                                        </div>
                                        <input type="number"
                                               name="fee_mappings[<?php echo $group['main']['id']; ?>][amount]"
                                               class="amount-input main-amount"
                                               placeholder="পরিমাণ (৳)"
                                               step="0.01"
                                               min="0"
                                               data-main-id="<?php echo $group['main']['id']; ?>"
                                               value="<?php echo isset($current_mappings[$group['main']['id']]) ? $current_mappings[$group['main']['id']]['amount'] : ''; ?>">
                                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                                            <input type="checkbox"
                                                   name="fee_mappings[<?php echo $group['main']['id']; ?>][mandatory]"
                                                   class="mandatory-checkbox"
                                                   <?php echo (isset($current_mappings[$group['main']['id']]) && $current_mappings[$group['main']['id']]['is_mandatory']) ? 'checked' : ''; ?>>
                                            <span>বাধ্যতামূলক</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Sub Categories -->
                                <?php foreach ($group['subs'] as $sub): ?>
                                    <div class="sub-category-item">
                                        <div class="category-name">
                                            <div>
                                                <i class="fas fa-arrow-right" style="margin-left: 1rem; color: #6c757d;"></i>
                                                <?php echo htmlspecialchars($sub['category_name']); ?>
                                                <?php if ($sub['description']): ?>
                                                    <small style="color: #6c757d; display: block; margin-left: 1.5rem;">
                                                        <?php echo htmlspecialchars($sub['description']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="category-actions" style="display: flex; gap: 0.5rem;">
                                                <button type="button" class="btn-action btn-edit" onclick="editCategory(<?php echo $sub['id']; ?>, '<?php echo htmlspecialchars($sub['category_name']); ?>', '<?php echo htmlspecialchars($sub['description']); ?>')" title="সম্পাদনা">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn-action btn-delete" onclick="deleteCategory(<?php echo $sub['id']; ?>, '<?php echo htmlspecialchars($sub['category_name']); ?>')" title="মুছে ফেলুন">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <input type="number"
                                               name="fee_mappings[<?php echo $sub['id']; ?>][amount]"
                                               class="amount-input sub-amount"
                                               placeholder="পরিমাণ (৳)"
                                               step="0.01"
                                               min="0"
                                               data-main-id="<?php echo $sub['parent_id']; ?>"
                                               value="<?php echo isset($current_mappings[$sub['id']]) ? $current_mappings[$sub['id']]['amount'] : ''; ?>">
                                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                                            <input type="checkbox"
                                                   name="fee_mappings[<?php echo $sub['id']; ?>][mandatory]"
                                                   class="mandatory-checkbox"
                                                   <?php echo (isset($current_mappings[$sub['id']]) && $current_mappings[$sub['id']]['is_mandatory']) ? 'checked' : ''; ?>>
                                            <span>বাধ্যতামূলক</span>
                                        </label>
                                        <div>
                                            <i class="fas fa-sitemap" style="color: #28a745;" title="সাব ক্যাটেগরি"></i>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endforeach; ?>
                        
                        <div style="text-align: center; margin-top: 2rem;">
                            <button type="submit" class="btn btn-success" style="font-size: 1.1rem; padding: 1rem 2rem;">
                                <i class="fas fa-save"></i> ফি ম্যাপিং সংরক্ষণ করুন
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Bulk Copy Section -->
                <div class="card">
                    <div class="bulk-copy-section">
                        <h3><i class="fas fa-copy"></i> বাল্ক কপি সিস্টেম</h3>
                        <p>একটি ক্লাসের ফি ম্যাপিং অন্য ক্লাসে কপি করুন</p>

                        <form method="POST" style="margin-top: 1.5rem;">
                            <input type="hidden" name="action" value="bulk_copy">
                            <input type="hidden" name="academic_year" value="<?php echo $selected_year; ?>">

                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">সোর্স ক্লাস (যেখান থেকে কপি করবেন)</label>
                                    <select name="source_class" class="form-control" required>
                                        <option value="">-- সোর্স ক্লাস নির্বাচন করুন --</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>"
                                                    <?php echo $selected_class == $class['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">টার্গেট ক্লাস (যেখানে কপি করবেন)</label>
                                    <div class="checkbox-group">
                                        <?php foreach ($classes as $class): ?>
                                            <div class="checkbox-item">
                                                <input type="checkbox"
                                                       name="target_classes[]"
                                                       value="<?php echo $class['id']; ?>"
                                                       id="target_<?php echo $class['id']; ?>">
                                                <label for="target_<?php echo $class['id']; ?>">
                                                    <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: center; margin-top: 1rem;">
                                <button type="submit" class="btn btn-info" onclick="return confirm('আপনি কি নিশ্চিত যে ফি ম্যাপিং কপি করতে চান?')">
                                    <i class="fas fa-copy"></i> বাল্ক কপি করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <h3><i class="fas fa-bolt"></i> দ্রুত কার্যক্রম</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                        <a href="fees.php" class="btn btn-primary">
                            <i class="fas fa-money-bill-wave"></i> ফি সংগ্রহ
                        </a>
                        <a href="fee_structure.php" class="btn btn-info">
                            <i class="fas fa-list"></i> ফি কাঠামো
                        </a>
                        <a href="fee_reports.php" class="btn btn-success">
                            <i class="fas fa-chart-bar"></i> ফি রিপোর্ট
                        </a>
                        <a href="fees.php" class="btn btn-primary">
                            <i class="fas fa-cog"></i> ফি ক্যাটেগরি
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Sub Category Modal -->
    <div id="addSubCategoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus-circle"></i> নতুন সাব ক্যাটেগরি যোগ করুন
                </h3>
                <button type="button" class="close" onclick="closeModal('addSubCategoryModal')">&times;</button>
            </div>

            <form method="POST" id="addSubCategoryForm">
                <input type="hidden" name="action" value="add_sub_category">
                <input type="hidden" name="parent_id" id="modal_parent_id">

                <div class="form-group">
                    <label class="form-label">প্যারেন্ট ক্যাটেগরি</label>
                    <input type="text" id="modal_parent_name" class="form-control" readonly style="background-color: #f8f9fa;">
                </div>

                <div class="form-group">
                    <label class="form-label">সাব ক্যাটেগরির নাম <span style="color: red;">*</span></label>
                    <input type="text" name="category_name" class="form-control" placeholder="যেমন: ভর্তি ফি, মাসিক ফি, পরীক্ষা ফি" required>
                </div>

                <div class="form-group">
                    <label class="form-label">বিবরণ</label>
                    <textarea name="description" class="form-control" rows="3" placeholder="এই ক্যাটেগরি সম্পর্কে বিস্তারিত লিখুন (ঐচ্ছিক)"></textarea>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addSubCategoryModal')" style="margin-right: 1rem;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus"></i> সাব ক্যাটেগরি যোগ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="editCategoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-edit"></i> ক্যাটেগরি সম্পাদনা করুন
                </h3>
                <button type="button" class="close" onclick="closeModal('editCategoryModal')">&times;</button>
            </div>

            <form method="POST" id="editCategoryForm">
                <input type="hidden" name="action" value="edit_category">
                <input type="hidden" name="category_id" id="edit_category_id">

                <div class="form-group">
                    <label class="form-label">ক্যাটেগরির নাম <span style="color: red;">*</span></label>
                    <input type="text" name="category_name" id="edit_category_name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label class="form-label">বিবরণ</label>
                    <textarea name="description" id="edit_category_description" class="form-control" rows="3"></textarea>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('editCategoryModal')" style="margin-right: 1rem;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> আপডেট করুন
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Category Modal -->
    <div id="deleteCategoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" style="color: #dc3545;">
                    <i class="fas fa-exclamation-triangle"></i> ক্যাটেগরি মুছে ফেলুন
                </h3>
                <button type="button" class="close" onclick="closeModal('deleteCategoryModal')">&times;</button>
            </div>

            <div style="text-align: center; padding: 1rem;">
                <p style="font-size: 1.1rem; margin-bottom: 1.5rem;">
                    আপনি কি নিশ্চিত যে <strong id="delete_category_name"></strong> ক্যাটেগরি মুছে ফেলতে চান?
                </p>
                <p style="color: #dc3545; font-size: 0.9rem; margin-bottom: 2rem;">
                    <i class="fas fa-warning"></i> এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                </p>

                <form method="POST" id="deleteCategoryForm" style="display: inline;">
                    <input type="hidden" name="action" value="delete_category">
                    <input type="hidden" name="category_id" id="delete_category_id">

                    <button type="button" class="btn btn-secondary" onclick="closeModal('deleteCategoryModal')" style="margin-right: 1rem;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> মুছে ফেলুন
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Modal functions
        function showAddSubCategoryModal(parentId, parentName) {
            document.getElementById('modal_parent_id').value = parentId;
            document.getElementById('modal_parent_name').value = parentName;
            document.getElementById('addSubCategoryModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function editCategory(categoryId, categoryName, categoryDescription) {
            document.getElementById('edit_category_id').value = categoryId;
            document.getElementById('edit_category_name').value = categoryName;
            document.getElementById('edit_category_description').value = categoryDescription || '';
            document.getElementById('editCategoryModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function deleteCategory(categoryId, categoryName) {
            document.getElementById('delete_category_id').value = categoryId;
            document.getElementById('delete_category_name').textContent = categoryName;
            document.getElementById('deleteCategoryModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            if (modalId) {
                document.getElementById(modalId).style.display = 'none';
            } else {
                // Close all modals
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            }
            document.body.style.overflow = 'auto';

            // Reset forms
            document.querySelectorAll('.modal form').forEach(form => {
                form.reset();
            });
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                closeModal();
            }
        }

        // Calculate main category totals
        function calculateMainCategoryTotal(mainId) {
            let total = 0;

            // Get main category amount
            const mainInput = document.querySelector(`input[data-main-id="${mainId}"].main-amount`);
            if (mainInput && mainInput.value) {
                total += parseFloat(mainInput.value) || 0;
            }

            // Get all sub category amounts for this main category
            const subInputs = document.querySelectorAll(`input[data-main-id="${mainId}"].sub-amount`);
            subInputs.forEach(input => {
                if (input.value) {
                    total += parseFloat(input.value) || 0;
                }
            });

            // Update the total display
            const totalElement = document.getElementById(`main_total_${mainId}`);
            if (totalElement) {
                totalElement.textContent = total.toLocaleString('bn-BD', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + ' ৳';
            }

            return total;
        }

        // Auto-calculate total when amounts change
        document.addEventListener('DOMContentLoaded', function() {
            const amountInputs = document.querySelectorAll('.amount-input');

            function updateTotal() {
                let total = 0;
                amountInputs.forEach(input => {
                    const value = parseFloat(input.value) || 0;
                    total += value;
                });

                // Update summary if exists
                const totalElement = document.querySelector('.stat-item h3');
                if (totalElement) {
                    totalElement.textContent = total.toLocaleString('bn-BD', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) + ' ৳';
                }

                // Update main category totals
                const mainCategories = new Set();
                amountInputs.forEach(input => {
                    const mainId = input.getAttribute('data-main-id');
                    if (mainId) {
                        mainCategories.add(mainId);
                    }
                });

                mainCategories.forEach(mainId => {
                    calculateMainCategoryTotal(mainId);
                });
            }

            amountInputs.forEach(input => {
                input.addEventListener('input', updateTotal);
            });

            // Initial calculation
            updateTotal();

            // Select all checkboxes functionality
            const selectAllBtn = document.createElement('button');
            selectAllBtn.type = 'button';
            selectAllBtn.className = 'btn btn-info';
            selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> সব নির্বাচন করুন';
            selectAllBtn.style.marginBottom = '1rem';

            const checkboxGroup = document.querySelector('.checkbox-group');
            if (checkboxGroup) {
                checkboxGroup.parentNode.insertBefore(selectAllBtn, checkboxGroup);

                selectAllBtn.addEventListener('click', function() {
                    const checkboxes = checkboxGroup.querySelectorAll('input[type="checkbox"]');
                    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                    checkboxes.forEach(cb => {
                        cb.checked = !allChecked;
                    });

                    this.innerHTML = allChecked ?
                        '<i class="fas fa-check-square"></i> সব নির্বাচন করুন' :
                        '<i class="fas fa-square"></i> সব বাতিল করুন';
                });
            }
        });
    </script>
</body>
</html>
