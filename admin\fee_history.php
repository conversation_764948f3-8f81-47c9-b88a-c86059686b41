<?php
require_once '../config/database.php';
checkRole('admin');

$db = getDB();
$student_id = (int)($_GET['student_id'] ?? 0);
$message = '';
$error = '';

if (!$student_id) {
    header("Location: fees.php");
    exit;
}

// Fetch student details
$student = $db->selectOne("
    SELECT s.*, c.class_name, c.section
    FROM students s
    JOIN classes c ON s.class_id = c.id
    WHERE s.id = ?
", [$student_id]);

if (!$student) {
    header("Location: fees.php");
    exit;
}

// Check if fee_payments table exists, if not create it
try {
    $db->query("SELECT 1 FROM fee_payments LIMIT 1");
} catch (Exception $e) {
    // Create fee_payments table
    $db->query("
        CREATE TABLE IF NOT EXISTS fee_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            fee_structure_id INT NULL,
            fee_category_id INT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method ENUM('cash', 'bank', 'online') DEFAULT 'cash',
            receipt_number VARCHAR(50),
            remarks TEXT,
            collected_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
        )
    ");
}

// Fetch fee payment history
$fee_history = $db->select("
    SELECT 
        fp.*,
        fs.fee_type,
        fc.category_name,
        u.username as collected_by_name
    FROM fee_payments fp
    LEFT JOIN fee_structure fs ON fp.fee_structure_id = fs.id
    LEFT JOIN fee_categories fc ON fp.fee_category_id = fc.id
    LEFT JOIN users u ON fp.collected_by = u.id
    WHERE fp.student_id = ?
    ORDER BY fp.payment_date DESC, fp.created_at DESC
", [$student_id]);

// Calculate total paid amount
$total_paid = $db->selectOne("
    SELECT COALESCE(SUM(amount), 0) as total
    FROM fee_payments
    WHERE student_id = ?
", [$student_id])['total'] ?? 0;

// Get fee structure for student's class
$fee_structure = $db->select("
    SELECT * FROM fee_structure 
    WHERE class_id = ?
    ORDER BY fee_type
", [$student['class_id']]);

// Calculate total due amount
$total_due = 0;
foreach ($fee_structure as $fee) {
    $total_due += $fee['amount'];
}

$balance = $total_due - $total_paid;

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ইতিহাস - <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fee-history-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .student-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .balance-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .balance-card {
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            color: white;
        }
        
        .balance-card.paid {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .balance-card.due {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }
        
        .balance-card.balance {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        
        .balance-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        .balance-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .payment-method {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .payment-method.cash {
            background-color: #d4edda;
            color: #155724;
        }
        
        .payment-method.bank {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .payment-method.online {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .no-data {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .no-data i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="fee-history-container">
        <div class="container">
            <!-- Back Button -->
            <div style="margin-bottom: 1rem;">
                <a href="fees.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> ফি ব্যবস্থাপনায় ফিরুন
                </a>
            </div>

            <!-- Student Information -->
            <div class="student-info">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <?php if ($student['photo']): ?>
                        <img src="../uploads/students/<?php echo htmlspecialchars($student['photo']); ?>" 
                             alt="ছাত্রের ছবি" 
                             style="width: 80px; height: 80px; border-radius: 50%; object-fit: cover; border: 3px solid white;">
                    <?php else: ?>
                        <div style="width: 80px; height: 80px; border-radius: 50%; background: rgba(255,255,255,0.2); display: flex; align-items: center; justify-content: center; border: 3px solid white;">
                            <i class="fas fa-user" style="font-size: 2rem;"></i>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h1 style="margin: 0; font-size: 2rem;">
                            <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                        </h1>
                        <p style="margin: 0.5rem 0 0 0; opacity: 0.9; font-size: 1.1rem;">
                            <i class="fas fa-id-card"></i> ছাত্র ID: <?php echo htmlspecialchars($student['student_id']); ?> |
                            <i class="fas fa-graduation-cap"></i> ক্লাস: <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?> |
                            <i class="fas fa-list-ol"></i> রোল: <?php echo htmlspecialchars($student['roll_number']); ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Balance Summary -->
            <div class="balance-cards">
                <div class="balance-card paid">
                    <h3><?php echo number_format($total_paid, 2); ?> ৳</h3>
                    <p><i class="fas fa-check-circle"></i> মোট পরিশোধিত</p>
                </div>
                <div class="balance-card due">
                    <h3><?php echo number_format($total_due, 2); ?> ৳</h3>
                    <p><i class="fas fa-clock"></i> মোট বকেয়া</p>
                </div>
                <div class="balance-card balance">
                    <h3><?php echo number_format(abs($balance), 2); ?> ৳</h3>
                    <p>
                        <i class="fas fa-<?php echo $balance > 0 ? 'exclamation-triangle' : 'check'; ?>"></i>
                        <?php echo $balance > 0 ? 'বাকি আছে' : 'সম্পূর্ণ পরিশোধিত'; ?>
                    </p>
                </div>
            </div>

            <!-- Payment History -->
            <div class="card">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 1.5rem;">
                    <h2 style="margin: 0;"><i class="fas fa-history"></i> ফি পেমেন্ট ইতিহাস</h2>
                    <a href="collect_fee.php?student_id=<?php echo $student_id; ?>" class="btn btn-success">
                        <i class="fas fa-plus"></i> নতুন পেমেন্ট যোগ করুন
                    </a>
                </div>

                <?php if (!empty($fee_history)): ?>
                    <div style="overflow-x: auto;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-calendar"></i> তারিখ</th>
                                    <th><i class="fas fa-tag"></i> ফির ধরন</th>
                                    <th><i class="fas fa-money-bill"></i> পরিমাণ</th>
                                    <th><i class="fas fa-credit-card"></i> পেমেন্ট পদ্ধতি</th>
                                    <th><i class="fas fa-receipt"></i> রসিদ নং</th>
                                    <th><i class="fas fa-user"></i> সংগ্রহকারী</th>
                                    <th><i class="fas fa-sticky-note"></i> মন্তব্য</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($fee_history as $payment): ?>
                                    <tr>
                                        <td><?php echo date('d/m/Y', strtotime($payment['payment_date'])); ?></td>
                                        <td>
                                            <?php 
                                            if ($payment['fee_type']) {
                                                echo htmlspecialchars($payment['fee_type']);
                                            } elseif ($payment['category_name']) {
                                                echo htmlspecialchars($payment['category_name']);
                                            } else {
                                                echo '<span style="color: #6c757d;">অনির্দিষ্ট</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><strong><?php echo number_format($payment['amount'], 2); ?> ৳</strong></td>
                                        <td>
                                            <span class="payment-method <?php echo $payment['payment_method']; ?>">
                                                <?php 
                                                switch($payment['payment_method']) {
                                                    case 'cash': echo 'নগদ'; break;
                                                    case 'bank': echo 'ব্যাংক'; break;
                                                    case 'online': echo 'অনলাইন'; break;
                                                    default: echo 'অজানা';
                                                }
                                                ?>
                                            </span>
                                        </td>
                                        <td><?php echo $payment['receipt_number'] ? htmlspecialchars($payment['receipt_number']) : '-'; ?></td>
                                        <td><?php echo $payment['collected_by_name'] ? htmlspecialchars($payment['collected_by_name']) : '-'; ?></td>
                                        <td><?php echo $payment['remarks'] ? htmlspecialchars($payment['remarks']) : '-'; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="no-data">
                        <i class="fas fa-receipt"></i>
                        <h3>কোনো পেমেন্ট রেকর্ড পাওয়া যায়নি</h3>
                        <p>এই ছাত্রের জন্য এখনো কোনো ফি পেমেন্ট করা হয়নি।</p>
                        <a href="collect_fee.php?student_id=<?php echo $student_id; ?>" class="btn btn-success">
                            <i class="fas fa-plus"></i> প্রথম পেমেন্ট যোগ করুন
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
