<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Teachers Table Structure Check</h2>";
    
    // Check if teachers table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'teachers'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>Teachers table does not exist. Creating it...</p>";
        
        // Create teachers table
        $sql = "CREATE TABLE teachers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            teacher_id VARCHAR(20) UNIQUE NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
            email VARCHAR(150) UNIQUE,
            phone VARCHAR(20),
            address TEXT,
            qualification VARCHAR(200),
            subject_specialization VARCHAR(100),
            joining_date DATE,
            salary DECIMAL(10,2),
            status ENUM('active', 'inactive') DEFAULT 'active',
            photo VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>Teachers table created successfully!</p>";
        
        // Insert sample data
        $sampleTeachers = [
            ['TCH001', 'ফাতেমা', 'খাতুন', '<EMAIL>', '01722222222', 'ঢাকা', 'এমএ (বাংলা)', 'বাংলা', '2020-01-15', 25000, 'active'],
            ['TCH002', 'মোহাম্মদ', 'রহিম', '<EMAIL>', '01733333333', 'চট্টগ্রাম', 'এমএসসি (গণিত)', 'গণিত', '2019-03-10', 28000, 'active'],
            ['TCH003', 'সালমা', 'বেগম', '<EMAIL>', '01744444444', 'সিলেট', 'এমএ (ইংরেজি)', 'ইংরেজি', '2021-06-20', 26000, 'active'],
            ['TCH004', 'আব্দুল', 'করিম', '<EMAIL>', '01755555555', 'রাজশাহী', 'এমএসসি (পদার্থবিজ্ঞান)', 'পদার্থবিজ্ঞান', '2018-09-05', 30000, 'active'],
            ['TCH005', 'রোকেয়া', 'আক্তার', '<EMAIL>', '01766666666', 'বরিশাল', 'এমএ (ইতিহাস)', 'ইতিহাস', '2022-01-12', 24000, 'active']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, address, qualification, subject_specialization, joining_date, salary, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sampleTeachers as $teacher) {
            $stmt->execute($teacher);
        }
        
        echo "<p style='color: green;'>Sample teachers data inserted!</p>";
        
    } else {
        echo "<p style='color: green;'>Teachers table exists.</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE teachers");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Current Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $hasPhoto = false;
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
            
            if ($column['Field'] == 'photo') {
                $hasPhoto = true;
            }
        }
        echo "</table>";
        
        // Add photo column if missing
        if (!$hasPhoto) {
            echo "<p style='color: orange;'>Photo column missing. Adding it...</p>";
            $pdo->exec("ALTER TABLE teachers ADD COLUMN photo VARCHAR(255) DEFAULT NULL");
            echo "<p style='color: green;'>Photo column added successfully!</p>";
        } else {
            echo "<p style='color: green;'>Photo column exists.</p>";
        }
        
        // Show current data
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM teachers");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<h3>Current Data: {$count} teachers</h3>";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT * FROM teachers LIMIT 5");
            $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Teacher ID</th><th>Name</th><th>Email</th><th>Phone</th><th>Subject</th><th>Status</th></tr>";
            
            foreach ($teachers as $teacher) {
                echo "<tr>";
                echo "<td>" . ($teacher['id'] ?? '') . "</td>";
                echo "<td>" . ($teacher['teacher_id'] ?? '') . "</td>";
                echo "<td>" . ($teacher['first_name'] ?? '') . " " . ($teacher['last_name'] ?? '') . "</td>";
                echo "<td>" . ($teacher['email'] ?? '') . "</td>";
                echo "<td>" . ($teacher['phone'] ?? '') . "</td>";
                echo "<td>" . ($teacher['subject_specialization'] ?? '') . "</td>";
                echo "<td>" . ($teacher['status'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<a href="teachers.php" style="display: inline-block; margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">Go to Teachers Page</a>
