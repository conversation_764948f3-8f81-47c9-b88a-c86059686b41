-- বিদ্যম<PERSON>ন students টেবিলে নতুন কলাম যোগ করার জন্য আপডেট স্ক্রিপ্ট
-- Update script to add new columns to existing students table

-- ছবির জন্য কলাম যোগ করা
ALTER TABLE students ADD COLUMN photo VARCHAR(255) AFTER gender;

-- পিতা-মাতার নামের জন্য কলাম যোগ করা
ALTER TABLE students ADD COLUMN father_name VARCHAR(100) AFTER photo;
ALTER TABLE students ADD COLUMN mother_name VARCHAR(100) AFTER father_name;

-- অভিভাবকের তথ্যের জন্য কলাম যোগ করা
ALTER TABLE students ADD COLUMN guardian_name VARCHAR(100) AFTER mother_name;
ALTER TABLE students ADD COLUMN guardian_relation VARCHAR(50) AFTER guardian_name;
ALTER TABLE students ADD COLUMN guardian_phone VARCHAR(15) AFTER guardian_relation;
ALTER TABLE students ADD COLUMN guardian_email VARCHAR(100) AFTER guardian_phone;
ALTER TABLE students ADD COLUMN guardian_address TEXT AFTER guardian_email;

-- বি<PERSON><PERSON>যমান ডেটা আপডেট করা (যদি কোন ডেটা থাকে)
UPDATE students SET 
    father_name = 'পিতার নাম',
    mother_name = 'মাতার নাম',
    guardian_name = 'অভিভাবকের নাম',
    guardian_relation = 'পিতা',
    guardian_phone = parent_phone,
    guardian_email = CONCAT('guardian', id, '@email.com')
WHERE father_name IS NULL;
