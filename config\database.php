<?php
/**
 * ডাটাবেস কানেকশন ফাইল
 * Database Connection File
 * স্কুল ব্যবস্থাপনা সিস্টেম
 */

// ডাটাবেস কনফিগারেশন
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'school_management');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $username = DB_USERNAME;
    private $password = DB_PASSWORD;
    private $database = DB_NAME;
    private $charset = DB_CHARSET;
    private $connection;
    
    public function __construct() {
        $this->connect();
    }
    
    /**
     * ডাটাবেস কানেকশন তৈরি করা
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->database};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            die("ডাটাবেস কানেকশন ব্যর্থ: " . $e->getMessage());
        }
    }
    
    /**
     * কানেকশন অবজেক্ট রিটার্ন করা
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * SELECT কুয়েরি চালানো
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Select Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * একটি রো SELECT করা
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("SelectOne Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * INSERT কুয়েরি চালানো
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            return $result ? $this->connection->lastInsertId() : false;
        } catch (PDOException $e) {
            error_log("Insert Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * UPDATE কুয়েরি চালানো
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Update Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * DELETE কুয়েরি চালানো
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Delete Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * কাস্টম কুয়েরি চালানো
     */
    public function query($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Query Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * ট্রানজেকশন শুরু করা
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * ট্রানজেকশন কমিট করা
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * ট্রানজেকশন রোলব্যাক করা
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * রো কাউন্ট করা
     */
    public function count($table, $where = '', $params = []) {
        try {
            $query = "SELECT COUNT(*) as count FROM {$table}";
            if (!empty($where)) {
                $query .= " WHERE {$where}";
            }
            
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            $result = $stmt->fetch();
            
            return $result['count'];
        } catch (PDOException $e) {
            error_log("Count Error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * টেবিল এক্সিস্ট করে কিনা চেক করা
     */
    public function tableExists($table) {
        try {
            $query = "SHOW TABLES LIKE ?";
            $stmt = $this->connection->prepare($query);
            $stmt->execute([$table]);
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Table Exists Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * ডাটাবেস কানেকশন বন্ধ করা
     */
    public function close() {
        $this->connection = null;
    }
}

// গ্লোবাল ডাটাবেস ইনস্ট্যান্স
$database = new Database();

/**
 * ডাটাবেস কানেকশন পাওয়ার জন্য হেল্পার ফাংশন
 */
function getDB() {
    global $database;
    return $database;
}

/**
 * ডাটাবেস কানেকশন অবজেক্ট পাওয়ার জন্য হেল্পার ফাংশন
 */
function getConnection() {
    global $database;
    return $database->getConnection();
}

/**
 * SQL ইনজেকশন প্রতিরোধের জন্য ডেটা স্যানিটাইজ করা
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * পাসওয়ার্ড হ্যাশ করা
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * পাসওয়ার্ড ভেরিফাই করা
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * র‍্যান্ডম স্ট্রিং জেনারেট করা
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * JSON রেসপন্স পাঠানো
 */
function sendJsonResponse($data) {
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * সেশন চেক করা
 */
function checkSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

/**
 * ইউজার রোল চেক করা
 */
function checkRole($required_role) {
    checkSession();
    
    if ($_SESSION['role'] !== $required_role) {
        header('Location: unauthorized.php');
        exit;
    }
}

/**
 * ইউজার লগড ইন আছে কিনা চেক করা
 */
function isLoggedIn() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    return isset($_SESSION['user_id']);
}

/**
 * বর্তমান ইউজারের তথ্য পাওয়া
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $db = getDB();
    $query = "SELECT * FROM users WHERE id = ?";
    return $db->selectOne($query, [$_SESSION['user_id']]);
}
?>
