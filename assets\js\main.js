// স্কুল ব্যবস্থাপনা সিস্টেম JavaScript
// School Management System JavaScript

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    // Initialize modals
    initializeModals();
    
    // Initialize form validations
    initializeFormValidations();
    
    // Initialize AJAX forms
    initializeAjaxForms();
    
    // Initialize data tables
    initializeDataTables();
}

// Modal Functions
function initializeModals() {
    // Get all modal triggers
    const modalTriggers = document.querySelectorAll('[data-modal]');
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.close');

    // Open modal
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
                makeDraggable(modal);
            }
        });
    });

    // Close modal
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Close modal when clicking outside
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
            }
        });
    });
}

// Form Validation
function initializeFormValidations() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    // Clear previous errors
    clearFormErrors(form);
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'এই ফিল্ডটি আবশ্যক');
            isValid = false;
        }
    });
    
    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'সঠিক ইমেইল ঠিকানা দিন');
            isValid = false;
        }
    });
    
    // Phone validation
    const phoneFields = form.querySelectorAll('input[type="tel"]');
    phoneFields.forEach(field => {
        if (field.value && !isValidPhone(field.value)) {
            showFieldError(field, 'সঠিক ফোন নম্বর দিন');
            isValid = false;
        }
    });
    
    return isValid;
}

function clearFormErrors(form) {
    const errorMessages = form.querySelectorAll('.error-message');
    errorMessages.forEach(error => error.remove());
    
    const errorFields = form.querySelectorAll('.error');
    errorFields.forEach(field => field.classList.remove('error'));
}

function showFieldError(field, message) {
    field.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.color = '#dc3545';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]{10,15}$/;
    return phoneRegex.test(phone);
}

// AJAX Functions
function initializeAjaxForms() {
    const ajaxForms = document.querySelectorAll('form[data-ajax]');
    
    ajaxForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitFormAjax(this);
        });
    });
}

function submitFormAjax(form) {
    const formData = new FormData(form);
    const url = form.getAttribute('action') || window.location.href;
    const method = form.getAttribute('method') || 'POST';
    
    // Show loading
    showLoading(form);
    
    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading(form);
        handleAjaxResponse(data, form);
    })
    .catch(error => {
        hideLoading(form);
        showAlert('একটি ত্রুটি ঘটেছে। আবার চেষ্টা করুন।', 'danger');
        console.error('Error:', error);
    });
}

function handleAjaxResponse(data, form) {
    if (data.success) {
        showAlert(data.message || 'সফলভাবে সম্পন্ন হয়েছে', 'success');
        
        // Reset form if specified
        if (data.reset_form) {
            form.reset();
        }
        
        // Redirect if specified
        if (data.redirect) {
            setTimeout(() => {
                window.location.href = data.redirect;
            }, 1500);
        }
        
        // Reload data if specified
        if (data.reload_data) {
            loadData(data.reload_data);
        }
        
        // Close modal if form is in modal
        const modal = form.closest('.modal');
        if (modal) {
            modal.style.display = 'none';
        }
    } else {
        showAlert(data.message || 'একটি ত্রুটি ঘটেছে', 'danger');
        
        // Show field errors if provided
        if (data.errors) {
            showFormErrors(form, data.errors);
        }
    }
}

function showFormErrors(form, errors) {
    Object.keys(errors).forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            showFieldError(field, errors[fieldName]);
        }
    });
}

// Data Loading Functions
function loadData(endpoint, container = null) {
    showLoading(container);
    
    fetch(endpoint)
    .then(response => response.text())
    .then(html => {
        hideLoading(container);
        if (container) {
            container.innerHTML = html;
        }
    })
    .catch(error => {
        hideLoading(container);
        showAlert('ডেটা লোড করতে সমস্যা হয়েছে', 'danger');
        console.error('Error:', error);
    });
}

// Utility Functions
function showLoading(container = null) {
    const spinner = document.createElement('div');
    spinner.className = 'spinner';
    spinner.id = 'loading-spinner';
    
    if (container) {
        container.appendChild(spinner);
    } else {
        document.body.appendChild(spinner);
    }
}

function hideLoading(container = null) {
    const spinner = document.getElementById('loading-spinner');
    if (spinner) {
        spinner.remove();
    }
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    // Insert at top of container
    const container = document.querySelector('.container') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

function confirmDelete(message = 'আপনি কি নিশ্চিত যে এটি মুছে ফেলতে চান?') {
    return confirm(message);
}

// Data Table Functions
function initializeDataTables() {
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        addTableSearch(table);
        addTableSort(table);
    });
}

function addTableSearch(table) {
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'অনুসন্ধান...';
    searchInput.className = 'form-control mb-2';
    
    table.parentNode.insertBefore(searchInput, table);
    
    searchInput.addEventListener('input', function() {
        filterTable(table, this.value);
    });
}

function filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm.toLowerCase())) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function addTableSort(table) {
    const headers = table.querySelectorAll('th');
    
    headers.forEach((header, index) => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(table, index);
        });
    });
}

function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        if (isAscending) {
            return aText.localeCompare(bText);
        } else {
            return bText.localeCompare(aText);
        }
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// Make Modal Draggable
function makeDraggable(modal) {
    const modalContent = modal.querySelector('.modal-content');
    const header = modal.querySelector('.modal-header');

    if (!header || !modalContent) return;

    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;
    let xOffset = 0;
    let yOffset = 0;

    // Reset position when modal opens
    modalContent.style.transform = 'translate(0px, 0px)';
    xOffset = 0;
    yOffset = 0;

    header.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);

    // Touch events for mobile
    header.addEventListener('touchstart', dragStart, { passive: false });
    document.addEventListener('touchmove', drag, { passive: false });
    document.addEventListener('touchend', dragEnd);

    function dragStart(e) {
        if (e.type === "touchstart") {
            initialX = e.touches[0].clientX - xOffset;
            initialY = e.touches[0].clientY - yOffset;
        } else {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
        }

        if (e.target === header || header.contains(e.target)) {
            // Don't drag if clicking on close button
            if (e.target.classList.contains('close') || e.target.closest('.close')) {
                return;
            }
            isDragging = true;
            modalContent.style.transition = 'none';
            header.style.cursor = 'grabbing';
        }
    }

    function drag(e) {
        if (isDragging) {
            e.preventDefault();

            if (e.type === "touchmove") {
                currentX = e.touches[0].clientX - initialX;
                currentY = e.touches[0].clientY - initialY;
            } else {
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
            }

            xOffset = currentX;
            yOffset = currentY;

            // Boundary checking
            const rect = modalContent.getBoundingClientRect();
            const maxX = window.innerWidth - rect.width;
            const maxY = window.innerHeight - rect.height;

            // Keep modal within viewport
            xOffset = Math.max(Math.min(xOffset, maxX / 2), -maxX / 2);
            yOffset = Math.max(Math.min(yOffset, maxY / 2), -maxY / 2);

            modalContent.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
        }
    }

    function dragEnd(e) {
        initialX = currentX;
        initialY = currentY;
        isDragging = false;
        modalContent.style.transition = '';
        header.style.cursor = 'move';
    }

    // Clean up event listeners when modal closes
    modal.addEventListener('hidden', function() {
        header.removeEventListener('mousedown', dragStart);
        document.removeEventListener('mousemove', drag);
        document.removeEventListener('mouseup', dragEnd);
        header.removeEventListener('touchstart', dragStart);
        document.removeEventListener('touchmove', drag);
        document.removeEventListener('touchend', dragEnd);
    });
}

// Export functions for global use
window.SchoolManagement = {
    showAlert,
    confirmDelete,
    loadData,
    submitFormAjax,
    showLoading,
    hideLoading,
    makeDraggable
};
