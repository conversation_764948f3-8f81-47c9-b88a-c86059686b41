<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';
$upload_results = [];

// Handle CSV preview
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'preview' && isset($_FILES['csv_file'])) {
    $csv_file = $_FILES['csv_file'];

    if ($csv_file['error'] === UPLOAD_ERR_OK && pathinfo($csv_file['name'], PATHINFO_EXTENSION) === 'csv') {
        $preview_data = previewCsvFile($csv_file['tmp_name']);
    } else {
        $error = 'বৈধ CSV ফাইল নির্বাচন করুন।';
    }
}

// Handle CSV upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload' && isset($_FILES['csv_file'])) {
    $csv_file = $_FILES['csv_file'];
    
    // Validate file
    if ($csv_file['error'] !== UPLOAD_ERR_OK) {
        $error = 'ফাইল আপলোড করতে সমস্যা হয়েছে।';
    } elseif ($csv_file['size'] > 5 * 1024 * 1024) { // 5MB limit
        $error = 'ফাইলের সাইজ অনেক বড়। সর্বোচ্চ ৫MB হতে পারে।';
    } elseif (pathinfo($csv_file['name'], PATHINFO_EXTENSION) !== 'csv') {
        $error = 'শুধুমাত্র CSV ফাইল আপলোড করুন।';
    } else {
        // Process CSV file
        $upload_results = processCsvFile($csv_file['tmp_name'], $pdo);
        if ($upload_results['success_count'] > 0) {
            $message = $upload_results['success_count'] . ' জন ছাত্র সফলভাবে যোগ করা হয়েছে।';
        }
        if ($upload_results['error_count'] > 0) {
            $error = $upload_results['error_count'] . ' জন ছাত্র যোগ করতে সমস্যা হয়েছে।';
        }
    }
}

// Get classes for dropdown
try {
    $stmt = $pdo->query("SELECT * FROM classes ORDER BY class_name");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $classes = [];
}

/**
 * Preview CSV file data
 */
function previewCsvFile($file_path) {
    $preview = [
        'headers' => [],
        'data' => [],
        'total_rows' => 0,
        'errors' => []
    ];

    if (($handle = fopen($file_path, "r")) !== FALSE) {
        // Read header
        $preview['headers'] = fgetcsv($handle);

        // Read first 5 rows for preview
        $row_count = 0;
        while (($data = fgetcsv($handle)) !== FALSE && $row_count < 5) {
            $preview['data'][] = $data;
            $row_count++;
        }

        // Count total rows
        while (($data = fgetcsv($handle)) !== FALSE) {
            $row_count++;
        }

        $preview['total_rows'] = $row_count;
        fclose($handle);
    }

    return $preview;
}

/**
 * Process CSV file and insert students
 */
function processCsvFile($file_path, $pdo) {
    $results = [
        'success_count' => 0,
        'error_count' => 0,
        'errors' => []
    ];
    
    if (($handle = fopen($file_path, "r")) !== FALSE) {
        $header = fgetcsv($handle); // Skip header row
        $row_number = 1;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $row_number++;
            
            try {
                // Map CSV columns to database fields
                $student_data = [
                    'student_id' => trim($data[0] ?? ''),
                    'first_name' => trim($data[1] ?? ''),
                    'last_name' => trim($data[2] ?? ''),
                    'class_name' => trim($data[3] ?? ''),
                    'roll_number' => (int)($data[4] ?? 0),
                    'date_of_birth' => trim($data[5] ?? ''),
                    'gender' => trim($data[6] ?? ''),
                    'father_name' => trim($data[7] ?? ''),
                    'mother_name' => trim($data[8] ?? ''),
                    'guardian_name' => trim($data[9] ?? ''),
                    'guardian_relation' => trim($data[10] ?? ''),
                    'guardian_phone' => trim($data[11] ?? ''),
                    'guardian_email' => trim($data[12] ?? ''),
                    'guardian_address' => trim($data[13] ?? ''),
                    'phone' => trim($data[14] ?? ''),
                    'parent_phone' => trim($data[15] ?? ''),
                    'address' => trim($data[16] ?? ''),
                    'username' => trim($data[17] ?? ''),
                    'email' => trim($data[18] ?? ''),
                    'password' => trim($data[19] ?? 'student123') // Default password
                ];
                
                // Validate required fields only
                if (empty($student_data['student_id']) || empty($student_data['first_name']) ||
                    empty($student_data['last_name']) || empty($student_data['class_name']) ||
                    empty($student_data['username']) || empty($student_data['email'])) {
                    $results['errors'][] = "Row $row_number: Required fields missing (student_id, first_name, last_name, class_name, username, email)";
                    $results['error_count']++;
                    continue;
                }

                // Set default values for optional fields if empty
                $student_data['roll_number'] = $student_data['roll_number'] ?: 0;
                $student_data['date_of_birth'] = $student_data['date_of_birth'] ?: null;
                $student_data['gender'] = $student_data['gender'] ?: null;
                $student_data['father_name'] = $student_data['father_name'] ?: '';
                $student_data['mother_name'] = $student_data['mother_name'] ?: '';
                $student_data['guardian_name'] = $student_data['guardian_name'] ?: '';
                $student_data['guardian_relation'] = $student_data['guardian_relation'] ?: '';
                $student_data['guardian_phone'] = $student_data['guardian_phone'] ?: '';
                $student_data['guardian_email'] = $student_data['guardian_email'] ?: '';
                $student_data['guardian_address'] = $student_data['guardian_address'] ?: '';
                $student_data['phone'] = $student_data['phone'] ?: '';
                $student_data['parent_phone'] = $student_data['parent_phone'] ?: '';
                $student_data['address'] = $student_data['address'] ?: '';
                
                // Get class ID
                $stmt = $pdo->prepare("SELECT id FROM classes WHERE class_name = ?");
                $stmt->execute([$student_data['class_name']]);
                $class = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$class) {
                    $results['errors'][] = "Row $row_number: Class '{$student_data['class_name']}' not found";
                    $results['error_count']++;
                    continue;
                }
                
                $class_id = $class['id'];
                
                // Check if student ID already exists
                $stmt = $pdo->prepare("SELECT id FROM students WHERE student_id = ?");
                $stmt->execute([$student_data['student_id']]);
                if ($stmt->fetch()) {
                    $results['errors'][] = "Row $row_number: Student ID '{$student_data['student_id']}' already exists";
                    $results['error_count']++;
                    continue;
                }
                
                // Check if username already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$student_data['username']]);
                if ($stmt->fetch()) {
                    $results['errors'][] = "Row $row_number: Username '{$student_data['username']}' already exists";
                    $results['error_count']++;
                    continue;
                }
                
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$student_data['email']]);
                if ($stmt->fetch()) {
                    $results['errors'][] = "Row $row_number: Email '{$student_data['email']}' already exists";
                    $results['error_count']++;
                    continue;
                }
                
                // Start transaction
                $pdo->beginTransaction();
                
                // Insert user
                $hashed_password = password_hash($student_data['password'], PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'student')");
                $stmt->execute([$student_data['username'], $hashed_password, $student_data['email']]);
                $user_id = $pdo->lastInsertId();
                
                // Insert student
                $stmt = $pdo->prepare("
                    INSERT INTO students (
                        user_id, student_id, first_name, last_name, class_id, roll_number, 
                        date_of_birth, gender, father_name, mother_name, guardian_name, 
                        guardian_relation, guardian_phone, guardian_email, guardian_address, 
                        phone, parent_phone, address, admission_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())
                ");
                
                $stmt->execute([
                    $user_id, $student_data['student_id'], $student_data['first_name'], 
                    $student_data['last_name'], $class_id, $student_data['roll_number'],
                    $student_data['date_of_birth'] ?: null, $student_data['gender'] ?: null,
                    $student_data['father_name'], $student_data['mother_name'], 
                    $student_data['guardian_name'], $student_data['guardian_relation'],
                    $student_data['guardian_phone'], $student_data['guardian_email'],
                    $student_data['guardian_address'], $student_data['phone'],
                    $student_data['parent_phone'], $student_data['address']
                ]);
                
                $pdo->commit();
                $results['success_count']++;
                
            } catch (Exception $e) {
                $pdo->rollback();
                $results['errors'][] = "Row $row_number: " . $e->getMessage();
                $results['error_count']++;
            }
        }
        fclose($handle);
    }
    
    return $results;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV আপলোড - ছাত্র ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .upload-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .upload-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .upload-title {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .upload-subtitle {
            color: #6c757d;
        }
        
        .csv-format-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .csv-format-title {
            color: #1976d2;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .csv-columns {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.5rem;
            font-size: 0.9rem;
        }
        
        .csv-column {
            background: white;
            padding: 0.5rem;
            border-radius: 4px;
            border-left: 3px solid #1976d2;
        }
        
        .required {
            color: #d32f2f;
            font-weight: 600;
        }
        
        .upload-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .file-input {
            width: 100%;
            padding: 1rem;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-input:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .file-input.dragover {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .upload-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1rem;
            transition: transform 0.3s ease;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        
        .upload-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .results-section {
            margin-top: 2rem;
        }
        
        .error-list {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .error-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #fed7d7;
            color: #c53030;
        }
        
        .error-item:last-child {
            border-bottom: none;
        }
        
        .sample-download {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .sample-btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        
        .sample-btn:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">📁 CSV ফাইল আপলোড</h1>
                <p class="content-subtitle">একসাথে অনেক ছাত্রের তথ্য যোগ করুন</p>
            </div>
            
            <div class="content-body">
                <div class="upload-container">
                    <div class="upload-header">
                        <h2 class="upload-title">📊 ছাত্র তথ্য CSV আপলোড</h2>
                        <p class="upload-subtitle">CSV ফাইলের মাধ্যমে একসাথে অনেক ছাত্রের তথ্য যোগ করুন</p>
                    </div>
                    
                    <!-- Sample Download -->
                    <div class="sample-download">
                        <a href="download_sample_csv.php" class="sample-btn">
                            📥 নমুনা CSV ফাইল ডাউনলোড করুন
                        </a>
                    </div>
                    
                    <!-- CSV Format Information -->
                    <div class="csv-format-info">
                        <h3 class="csv-format-title">📋 CSV ফাইল ফরম্যাট</h3>
                        <p>আপনার CSV ফাইলে নিচের কলামগুলো থাকতে হবে (একই ক্রমে):</p>
                        
                        <div class="csv-columns">
                            <div class="csv-column">1. <span class="required">ছাত্র ID*</span> (বাধ্যতামূলক)</div>
                            <div class="csv-column">2. <span class="required">নাম*</span> (বাধ্যতামূলক)</div>
                            <div class="csv-column">3. <span class="required">পদবি*</span> (বাধ্যতামূলক)</div>
                            <div class="csv-column">4. <span class="required">ক্লাস*</span> (বাধ্যতামূলক)</div>
                            <div class="csv-column">5. রোল নম্বর (ঐচ্ছিক)</div>
                            <div class="csv-column">6. জন্ম তারিখ (ঐচ্ছিক, YYYY-MM-DD)</div>
                            <div class="csv-column">7. লিঙ্গ (ঐচ্ছিক, male/female)</div>
                            <div class="csv-column">8. পিতার নাম (ঐচ্ছিক)</div>
                            <div class="csv-column">9. মাতার নাম (ঐচ্ছিক)</div>
                            <div class="csv-column">10. অভিভাবকের নাম (ঐচ্ছিক)</div>
                            <div class="csv-column">11. অভিভাবকের সম্পর্ক (ঐচ্ছিক)</div>
                            <div class="csv-column">12. অভিভাবকের ফোন (ঐচ্ছিক)</div>
                            <div class="csv-column">13. অভিভাবকের ইমেইল (ঐচ্ছিক)</div>
                            <div class="csv-column">14. অভিভাবকের ঠিকানা (ঐচ্ছিক)</div>
                            <div class="csv-column">15. ছাত্রের ফোন (ঐচ্ছিক)</div>
                            <div class="csv-column">16. পিতা-মাতার ফোন (ঐচ্ছিক)</div>
                            <div class="csv-column">17. ঠিকানা (ঐচ্ছিক)</div>
                            <div class="csv-column">18. <span class="required">ইউজারনেম*</span> (বাধ্যতামূলক)</div>
                            <div class="csv-column">19. <span class="required">ইমেইল*</span> (বাধ্যতামূলক)</div>
                            <div class="csv-column">20. পাসওয়ার্ড (ঐচ্ছিক, খালি থাকলে 'student123')</div>
                        </div>
                        
                        <p style="margin-top: 1rem;">
                            <span class="required">*</span> = বাধ্যতামূলক ফিল্ড<br>
                            <strong>📝 নোট:</strong> ঐচ্ছিক ফিল্ডগুলো খালি রাখা যাবে। শুধুমাত্র বাধ্যতামূলক ফিল্ডগুলো পূরণ করতে হবে।
                        </p>
                    </div>
                    
                    <!-- Upload Form -->
                    <form method="POST" enctype="multipart/form-data" class="upload-form" id="csvForm">
                        <div class="form-group">
                            <label class="form-label">CSV ফাইল নির্বাচন করুন:</label>
                            <div class="file-input-wrapper">
                                <input type="file" name="csv_file" accept=".csv" required class="form-control" id="csvFile">
                                <div class="file-input" onclick="document.getElementById('csvFile').click()">
                                    <div style="font-size: 3rem; margin-bottom: 1rem;">📁</div>
                                    <div>ক্লিক করুন বা ফাইল ড্র্যাগ করুন</div>
                                    <div style="font-size: 0.9rem; color: #6c757d; margin-top: 0.5rem;">
                                        সর্বোচ্চ ৫MB, শুধুমাত্র CSV ফাইল
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 1rem;">
                            <button type="submit" name="action" value="preview" class="upload-btn" style="flex: 1; background: #17a2b8;">
                                👁️ প্রিভিউ দেখুন
                            </button>
                            <button type="submit" name="action" value="upload" class="upload-btn" style="flex: 1;">
                                📤 আপলোড করুন
                            </button>
                        </div>
                    </form>

                    <!-- Preview Section -->
                    <?php if (isset($preview_data)): ?>
                        <div class="preview-section" style="margin-top: 2rem; background: #f8f9fa; border-radius: 8px; padding: 1.5rem;">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem;">👁️ CSV ফাইল প্রিভিউ</h3>
                            <p><strong>মোট রো:</strong> <?php echo $preview_data['total_rows']; ?> টি</p>

                            <?php if (!empty($preview_data['data'])): ?>
                                <div style="overflow-x: auto; margin-top: 1rem;">
                                    <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden;">
                                        <thead style="background: #667eea; color: white;">
                                            <tr>
                                                <?php foreach ($preview_data['headers'] as $header): ?>
                                                    <th style="padding: 0.75rem; text-align: left; border-right: 1px solid rgba(255,255,255,0.2);">
                                                        <?php echo htmlspecialchars($header); ?>
                                                    </th>
                                                <?php endforeach; ?>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($preview_data['data'] as $row): ?>
                                                <tr style="border-bottom: 1px solid #dee2e6;">
                                                    <?php foreach ($row as $cell): ?>
                                                        <td style="padding: 0.75rem; border-right: 1px solid #dee2e6;">
                                                            <?php echo htmlspecialchars($cell); ?>
                                                        </td>
                                                    <?php endforeach; ?>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <div style="margin-top: 1rem; text-align: center;">
                                    <form method="POST" enctype="multipart/form-data" style="display: inline;">
                                        <input type="hidden" name="csv_file_path" value="<?php echo htmlspecialchars($csv_file['tmp_name']); ?>">
                                        <button type="submit" name="action" value="upload" class="upload-btn" style="width: auto; padding: 0.75rem 2rem;">
                                            ✅ নিশ্চিত করে আপলোড করুন
                                        </button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Results -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($upload_results['errors'])): ?>
                        <div class="results-section">
                            <h3>⚠️ আপলোড এরর তালিকা:</h3>
                            <div class="error-list">
                                <?php foreach ($upload_results['errors'] as $error_msg): ?>
                                    <div class="error-item"><?php echo htmlspecialchars($error_msg); ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script>
        // File drag and drop functionality
        const fileInput = document.getElementById('csvFile');
        const fileInputWrapper = document.querySelector('.file-input');
        
        fileInputWrapper.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        fileInputWrapper.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        fileInputWrapper.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileDisplay();
            }
        });
        
        fileInput.addEventListener('change', updateFileDisplay);
        
        function updateFileDisplay() {
            const file = fileInput.files[0];
            if (file) {
                fileInputWrapper.innerHTML = `
                    <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
                    <div><strong>${file.name}</strong></div>
                    <div style="font-size: 0.9rem; color: #6c757d; margin-top: 0.5rem;">
                        ${(file.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                `;
            }
        }
        
        // Form submission
        document.querySelector('.upload-form').addEventListener('submit', function() {
            const submitBtn = document.querySelector('.upload-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '⏳ আপলোড হচ্ছে...';
        });
    </script>
</body>
</html>
