<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্কুল ব্যবস্থাপনা সিস্টেম - হোম</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .main-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .feature-description {
            color: #6c757d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .feature-btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        
        .feature-btn:hover {
            transform: translateY(-2px);
        }
        
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .demo-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .demo-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .demo-link {
            display: block;
            padding: 1rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
        }
        
        .demo-link:hover {
            background: #e9ecef;
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .demo-link-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .demo-link-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .demo-link-desc {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #28a745;
            color: white;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">🏫 স্কুল ব্যবস্থাপনা সিস্টেম</h1>
            <p class="subtitle">আধুনিক এবং সুন্দর সাইডবার সহ সম্পূর্ণ স্কুল ব্যবস্থাপনা সমাধান</p>
            <div class="status-badge">✅ সাইডবার সম্পন্ন</div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">মোবাইল রেসপন্সিভ</h3>
                <p class="feature-description">সব ডিভাইসে সুন্দরভাবে কাজ করে। মোবাইলে টগল বাটন দিয়ে সাইডবার নিয়ন্ত্রণ করুন।</p>
                <a href="sidebar_demo.html" class="feature-btn">ডেমো দেখুন</a>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3 class="feature-title">সুন্দর ডিজাইন</h3>
                <p class="feature-description">গ্রেডিয়েন্ট ব্যাকগ্রাউন্ড, আধুনিক UI এবং স্মুথ অ্যানিমেশন সহ আকর্ষণীয় ইন্টারফেস।</p>
                <a href="test_dashboard.php" class="feature-btn">ড্যাশবোর্ড দেখুন</a>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔗</div>
                <h3 class="feature-title">স্মার্ট নেভিগেশন</h3>
                <p class="feature-description">সব গুরুত্বপূর্ণ বিভাগের জন্য সুসংগঠিত মেনু। বর্তমান পেজ অনুযায়ী অ্যাক্টিভ হাইলাইট।</p>
                <a href="admin/students.php" class="feature-btn">ছাত্র পেজ দেখুন</a>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🎯 ডেমো লিংকসমূহ</h2>
            <p>নিচের লিংকগুলো ব্যবহার করে সিস্টেমের বিভিন্ন অংশ দেখুন:</p>
            
            <div class="demo-links">
                <a href="simple_login.php" class="demo-link">
                    <div class="demo-link-icon">🔐</div>
                    <div class="demo-link-title">লগইন সিস্টেম</div>
                    <div class="demo-link-desc">admin/admin দিয়ে লগইন করুন</div>
                </a>
                
                <a href="sidebar_demo.html" class="demo-link">
                    <div class="demo-link-icon">📋</div>
                    <div class="demo-link-title">সাইডবার ডেমো</div>
                    <div class="demo-link-desc">সাইডবারের সব ফিচার দেখুন</div>
                </a>
                
                <a href="test_dashboard.php" class="demo-link">
                    <div class="demo-link-icon">📊</div>
                    <div class="demo-link-title">ড্যাশবোর্ড ডেমো</div>
                    <div class="demo-link-desc">সেশন ছাড়াই ড্যাশবোর্ড দেখুন</div>
                </a>
                
                <a href="admin/dashboard.php" class="demo-link">
                    <div class="demo-link-icon">🏠</div>
                    <div class="demo-link-title">প্রকৃত ড্যাশবোর্ড</div>
                    <div class="demo-link-desc">লগইন করার পর এই পেজ দেখুন</div>
                </a>
                
                <a href="admin/students.php" class="demo-link">
                    <div class="demo-link-icon">👨‍🎓</div>
                    <div class="demo-link-title">ছাত্র ব্যবস্থাপনা</div>
                    <div class="demo-link-desc">ছাত্র তালিকা এবং ফর্ম দেখুন</div>
                </a>
                
                <a href="includes/sidebar.php" class="demo-link">
                    <div class="demo-link-icon">🔧</div>
                    <div class="demo-link-title">সাইডবার কম্পোনেন্ট</div>
                    <div class="demo-link-desc">PHP সাইডবার কোড দেখুন</div>
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p>© ২০২৪ স্কুল ব্যবস্থাপনা সিস্টেম - সুন্দর সাইডবার সহ সম্পূর্ণ সমাধান</p>
            <p>🎉 সাইডবার সফলভাবে ইমপ্লিমেন্ট করা হয়েছে!</p>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card, .demo-link');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Add click animation
            const buttons = document.querySelectorAll('.feature-btn, .demo-link');
            buttons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Create ripple effect
                    const ripple = document.createElement('span');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255,255,255,0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = (e.clientX - this.offsetLeft) + 'px';
                    ripple.style.top = (e.clientY - this.offsetTop) + 'px';
                    
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
        
        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
