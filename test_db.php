<?php
// Test database structure
try {
    $pdo = new PDO("mysql:host=localhost;dbname=school_management;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>Database Structure Test</h3>";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'fee_categories'");
    if ($stmt->rowCount() > 0) {
        echo "✅ fee_categories table exists<br>";
        
        // Check columns
        $stmt = $pdo->query("DESCRIBE fee_categories");
        echo "<h4>Table Structure:</h4>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
        }
        
        // Check data
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM fee_categories");
        $total = $stmt->fetchColumn();
        echo "<br>Total categories: " . $total . "<br>";
        
        if ($total > 0) {
            $stmt = $pdo->query("SELECT id, category_name, parent_id, is_parent FROM fee_categories LIMIT 10");
            echo "<h4>Sample Data:</h4>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "ID: " . $row['id'] . " | Name: " . $row['category_name'] . " | Parent: " . ($row['parent_id'] ?? 'NULL') . " | Is Parent: " . ($row['is_parent'] ? 'YES' : 'NO') . "<br>";
            }
        }
        
    } else {
        echo "❌ fee_categories table does not exist<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
