http://localhost/skool/admin/budget.php<?php
require_once '../config/database.php';
checkRole('admin');

$db = getDB();
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Fetch classes for dropdowns
$classes = $db->select("SELECT * FROM classes WHERE status = 'active' ORDER BY class_name");

// Fetch students based on class filter
$class_filter = $_GET['class_id'] ?? null;
$students = [];
if ($class_filter) {
    $students = $db->select("
        SELECT s.id, s.student_id, s.first_name, s.last_name, c.class_name, c.section
        FROM students s
        JOIN classes c ON s.class_id = c.id
        WHERE s.class_id = ? AND s.status = 'active'
        ORDER BY s.roll_number
    ", [$class_filter]);
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">ফি ব্যবস্থাপনা</h1>
                <p class="content-subtitle">ছাত্রদের ফি দেখুন এবং ব্যবস্থাপনা করুন</p>
            </div>

            <div class="content-body">
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">ছাত্র নির্বাচন করুন</h2>
                    </div>
                    <div class="card-body">
                        <form action="" method="GET">
                            <div class="form-group">
                                <label for="class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                                <select id="class_id" name="class_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">-- ক্লাস নির্বাচন করুন --</option>
                                    <?php foreach ($classes as $class): ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($class_filter == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if ($class_filter && !empty($students)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h2 class="card-title">ছাত্রদের তালিকা</h2>
                    </div>
                    <div class="card-body">
                        <table class="table data-table">
                            <thead>
                                <tr>
                                    <th>ছাত্র ID</th>
                                    <th>নাম</th>
                                    <th>ক্লাস</th>
                                    <th>কার্যক্রম</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?></td>
                                        <td>
                                            <a href="collect_fee.php?student_id=<?php echo $student['id']; ?>" class="btn btn-primary btn-sm">ফি সংগ্রহ</a>
                                            <a href="fee_history.php?student_id=<?php echo $student['id']; ?>" class="btn btn-info btn-sm">ফি দেখুন</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php elseif ($class_filter): ?>
                    <div class="alert alert-info mt-4">এই ক্লাসে কোন ছাত্র পাওয়া যায়নি।</div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <script src="../assets/js/main.js"></script>
</body>
</html>