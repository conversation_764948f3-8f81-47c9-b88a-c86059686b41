<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Student Attendance Table Setup</h2>";
    
    // Check if student_attendance table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'student_attendance'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>Student attendance table does not exist. Creating it...</p>";
        
        // Create student_attendance table
        $sql = "CREATE TABLE student_attendance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            class_id VARCHAR(20) NOT NULL,
            attendance_date DATE NOT NULL,
            status ENUM('present', 'absent', 'late') NOT NULL,
            marked_by INT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
            UNIQUE KEY unique_attendance (student_id, attendance_date),
            INDEX idx_class_date (class_id, attendance_date),
            INDEX idx_student_date (student_id, attendance_date)
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>Student attendance table created successfully!</p>";
        
        // Insert sample attendance data
        echo "<p style='color: blue;'>Inserting sample attendance data...</p>";
        
        // Get some students
        $stmt = $pdo->query("SELECT id, class FROM students LIMIT 10");
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($students)) {
            $stmt = $pdo->prepare("
                INSERT INTO student_attendance (student_id, class_id, attendance_date, status, marked_by)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            // Create attendance for last 7 days
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-$i days"));
                
                foreach ($students as $student) {
                    // Random attendance status (90% present, 5% absent, 5% late)
                    $rand = rand(1, 100);
                    if ($rand <= 90) {
                        $status = 'present';
                    } elseif ($rand <= 95) {
                        $status = 'late';
                    } else {
                        $status = 'absent';
                    }
                    
                    try {
                        $stmt->execute([
                            $student['id'],
                            $student['class'],
                            $date,
                            $status,
                            1 // Admin user ID
                        ]);
                    } catch (PDOException $e) {
                        // Skip if duplicate
                        if ($e->getCode() != 23000) {
                            throw $e;
                        }
                    }
                }
            }
            
            echo "<p style='color: green;'>Sample attendance data inserted for last 7 days!</p>";
        } else {
            echo "<p style='color: orange;'>No students found. Please add students first.</p>";
        }
        
    } else {
        echo "<p style='color: green;'>Student attendance table already exists.</p>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE student_attendance");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Current Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show current data count
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM student_attendance");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<h3>Current Data: {$count} attendance records</h3>";
        
        if ($count > 0) {
            // Show recent attendance summary
            $stmt = $pdo->query("
                SELECT 
                    attendance_date,
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN status = 'present' THEN 1 END) as present,
                    COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent,
                    COUNT(CASE WHEN status = 'late' THEN 1 END) as late
                FROM student_attendance 
                WHERE attendance_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                GROUP BY attendance_date 
                ORDER BY attendance_date DESC
                LIMIT 7
            ");
            $summary = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($summary)) {
                echo "<h3>Recent Attendance Summary (Last 7 Days):</h3>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>Date</th><th>Total</th><th>Present</th><th>Absent</th><th>Late</th><th>Attendance %</th></tr>";
                
                foreach ($summary as $day) {
                    $attendance_percentage = $day['total_records'] > 0 ? 
                        round(($day['present'] / $day['total_records']) * 100, 1) : 0;
                    
                    echo "<tr>";
                    echo "<td>" . date('d/m/Y', strtotime($day['attendance_date'])) . "</td>";
                    echo "<td>" . $day['total_records'] . "</td>";
                    echo "<td style='color: green;'>" . $day['present'] . "</td>";
                    echo "<td style='color: red;'>" . $day['absent'] . "</td>";
                    echo "<td style='color: orange;'>" . $day['late'] . "</td>";
                    echo "<td><strong>" . $attendance_percentage . "%</strong></td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            // Show class-wise attendance
            $stmt = $pdo->query("
                SELECT 
                    s.class,
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present,
                    COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent,
                    COUNT(CASE WHEN sa.status = 'late' THEN 1 END) as late
                FROM student_attendance sa
                JOIN students s ON sa.student_id = s.id
                WHERE sa.attendance_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                GROUP BY s.class 
                ORDER BY s.class
            ");
            $classwise = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($classwise)) {
                echo "<h3>Class-wise Attendance (Last 7 Days):</h3>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>Class</th><th>Total Records</th><th>Present</th><th>Absent</th><th>Late</th><th>Attendance %</th></tr>";
                
                foreach ($classwise as $class) {
                    $attendance_percentage = $class['total_records'] > 0 ? 
                        round(($class['present'] / $class['total_records']) * 100, 1) : 0;
                    
                    echo "<tr>";
                    echo "<td>Class " . $class['class'] . "</td>";
                    echo "<td>" . $class['total_records'] . "</td>";
                    echo "<td style='color: green;'>" . $class['present'] . "</td>";
                    echo "<td style='color: red;'>" . $class['absent'] . "</td>";
                    echo "<td style='color: orange;'>" . $class['late'] . "</td>";
                    echo "<td><strong>" . $attendance_percentage . "%</strong></td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
    
    echo "<br><h3>Database Setup Complete!</h3>";
    echo "<p>✅ Student attendance system is ready to use.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>

<div style="margin-top: 30px;">
    <a href="student_attendance.php" style="display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        📋 Go to Student Attendance Page
    </a>
    
    <a href="teachers.php" style="display: inline-block; margin-left: 15px; padding: 15px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        👩‍🏫 Go to Teachers Page
    </a>
</div>
