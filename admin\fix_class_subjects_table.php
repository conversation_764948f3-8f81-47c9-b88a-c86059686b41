<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>🔧 Class-Subjects Table Fix</h2>";
    
    // Step 1: Check class_subjects table structure
    echo "<h3>Step 1: Checking class_subjects table structure</h3>";
    
    try {
        $stmt = $pdo->query("DESCRIBE class_subjects");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>Current columns: " . implode(', ', $columns) . "</p>";
        
        // Check if academic_year column exists
        if (!in_array('academic_year', $columns)) {
            echo "<p style='color: orange;'>⚠️ academic_year column missing in class_subjects table. Adding now...</p>";
            
            $pdo->exec("ALTER TABLE class_subjects ADD COLUMN academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "'");
            echo "<p style='color: green;'>✅ academic_year column added to class_subjects table!</p>";
            
            // Update existing records
            $pdo->exec("UPDATE class_subjects SET academic_year = '" . date('Y') . "' WHERE academic_year = '' OR academic_year IS NULL");
            echo "<p style='color: green;'>✅ Existing class_subjects records updated!</p>";
        } else {
            echo "<p style='color: green;'>✅ academic_year column already exists in class_subjects table!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ class_subjects table doesn't exist. Creating now...</p>";
        
        // Create the table with all required columns
        $sql = "CREATE TABLE class_subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_id INT NOT NULL,
            subject_id INT NOT NULL,
            academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "',
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
            UNIQUE KEY unique_class_subject (class_id, subject_id, academic_year)
        )";
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ class_subjects table created successfully!</p>";
    }
    
    // Step 2: Check classes table
    echo "<h3>Step 2: Checking classes table structure</h3>";
    $stmt = $pdo->query("DESCRIBE classes");
    $classes_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Classes table columns: " . implode(', ', $classes_columns) . "</p>";
    
    if (!in_array('academic_year', $classes_columns)) {
        echo "<p style='color: orange;'>⚠️ academic_year column missing in classes table. Adding now...</p>";
        
        $pdo->exec("ALTER TABLE classes ADD COLUMN academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "'");
        echo "<p style='color: green;'>✅ academic_year column added to classes table!</p>";
        
        $pdo->exec("UPDATE classes SET academic_year = '" . date('Y') . "' WHERE academic_year = '' OR academic_year IS NULL");
        echo "<p style='color: green;'>✅ Existing classes records updated!</p>";
    } else {
        echo "<p style='color: green;'>✅ academic_year column already exists in classes table!</p>";
    }
    
    // Step 3: Test the problematic query
    echo "<h3>Step 3: Testing the problematic query</h3>";
    
    try {
        $stmt = $pdo->prepare("
            SELECT c.id as class_id, c.class_name, c.section, c.academic_year,
                   COUNT(cs.id) as subject_count,
                   GROUP_CONCAT(s.subject_name ORDER BY s.subject_name SEPARATOR ', ') as subjects
            FROM classes c
            LEFT JOIN class_subjects cs ON c.id = cs.class_id AND cs.academic_year = ?
            LEFT JOIN subjects s ON cs.subject_id = s.id
            WHERE c.status = 'active'
            GROUP BY c.id, c.class_name, c.section, c.academic_year
            ORDER BY c.class_name, c.section
        ");
        $stmt->execute([date('Y')]);
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green;'>✅ Overview query successful - " . count($result) . " classes found</p>";
        
        // Show sample results
        if (!empty($result)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Class</th>";
            echo "<th style='padding: 8px;'>Section</th>";
            echo "<th style='padding: 8px;'>Year</th>";
            echo "<th style='padding: 8px;'>Subject Count</th>";
            echo "<th style='padding: 8px;'>Subjects</th>";
            echo "</tr>";
            
            foreach (array_slice($result, 0, 5) as $row) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$row['class_name']}</td>";
                echo "<td style='padding: 8px;'>{$row['section']}</td>";
                echo "<td style='padding: 8px;'>{$row['academic_year']}</td>";
                echo "<td style='padding: 8px;'>{$row['subject_count']}</td>";
                echo "<td style='padding: 8px;'>" . ($row['subjects'] ?: 'No subjects assigned') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Overview query failed: " . $e->getMessage() . "</p>";
    }
    
    // Step 4: Show final table structures
    echo "<h3>Step 4: Final Table Structures</h3>";
    
    echo "<h4>Classes Table:</h4>";
    $stmt = $pdo->query("DESCRIBE classes");
    $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Type</th><th style='padding: 8px;'>Null</th><th style='padding: 8px;'>Key</th><th style='padding: 8px;'>Default</th></tr>";
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Class-Subjects Table:</h4>";
    $stmt = $pdo->query("DESCRIBE class_subjects");
    $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Type</th><th style='padding: 8px;'>Null</th><th style='padding: 8px;'>Key</th><th style='padding: 8px;'>Default</th></tr>";
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 5: Add some sample data if needed
    echo "<h3>Step 5: Sample Data Check</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM classes");
    $class_count = $stmt->fetchColumn();
    
    if ($class_count == 0) {
        echo "<p style='color: orange;'>⚠️ No classes found. Adding sample classes...</p>";
        
        $sample_classes = [
            ['প্রথম শ্রেণী', 'ক', date('Y')],
            ['দ্বিতীয় শ্রেণী', 'ক', date('Y')],
            ['তৃতীয় শ্রেণী', 'ক', date('Y')],
            ['চতুর্থ শ্রেণী', 'ক', date('Y')],
            ['পঞ্চম শ্রেণী', 'ক', date('Y')]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year, status) VALUES (?, ?, ?, 'active')");
        foreach ($sample_classes as $class) {
            $stmt->execute($class);
            echo "<p style='color: green;'>✅ {$class[0]} - {$class[1]} added</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Found {$class_count} classes in database</p>";
    }
    
    echo "<h3>🎉 All Database Issues Fixed!</h3>";
    echo "<p style='color: green; font-weight: bold; font-size: 1.2em;'>The class-subjects system should now work perfectly without any column errors!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px; text-align: center;">
    <a href="class_subjects_fixed.php" style="display: inline-block; padding: 15px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;">
        🔗 ক্লাস-বিষয় সংযোগে যান
    </a>
    
    <a href="dashboard.php" style="display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;">
        🏠 ড্যাশবোর্ডে ফিরে যান
    </a>
</div>

<script>
// Auto redirect after 3 seconds
setTimeout(function() {
    window.location.href = 'class_subjects_fixed.php';
}, 3000);
</script>
