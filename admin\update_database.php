<?php
require_once '../config/database.php';

$db = new Database();
$pdo = $db->getConnection();

echo "<h2>Database Update Script</h2>";

try {
    // Check current table structure
    echo "<h3>Current students table structure:</h3>";
    $stmt = $pdo->query("DESCRIBE students");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Get existing column names
    $existing_columns = array_column($columns, 'Field');
    
    // Define required columns
    $required_columns = [
        'photo' => 'VARCHAR(255)',
        'father_name' => 'VARCHAR(100)',
        'mother_name' => 'VARCHAR(100)',
        'guardian_name' => 'VARCHAR(100)',
        'guardian_relation' => 'VARCHAR(50)',
        'guardian_phone' => 'VARCHAR(15)',
        'guardian_email' => 'VARCHAR(100)',
        'guardian_address' => 'TEXT',
        'phone' => 'VARCHAR(15)',
        'parent_phone' => 'VARCHAR(15)',
        'address' => 'TEXT'
    ];
    
    echo "<h3>Adding missing columns:</h3>";
    
    foreach ($required_columns as $column_name => $column_type) {
        if (!in_array($column_name, $existing_columns)) {
            try {
                $sql = "ALTER TABLE students ADD COLUMN $column_name $column_type";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ Added column: $column_name ($column_type)</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Error adding column $column_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ Column already exists: $column_name</p>";
        }
    }
    
    echo "<h3>Updated students table structure:</h3>";
    $stmt = $pdo->query("DESCRIBE students");
    $updated_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($updated_columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3 style='color: green;'>✅ Database update completed successfully!</h3>";
    echo "<p><a href='csv_upload.php'>Go back to CSV Upload</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ Database update failed:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
