<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

// Database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USERNAME, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Create fee categories table if not exists
$create_categories_table = "
CREATE TABLE IF NOT EXISTS fee_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    category_type ENUM('main', 'sub') DEFAULT 'main',
    parent_id INT NULL,
    amount DECIMAL(10,2) DEFAULT 0,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES fee_categories(id) ON DELETE CASCADE
)";

try {
    $pdo->exec($create_categories_table);

    // Check if we need to add default categories
    $count_stmt = $pdo->query("SELECT COUNT(*) FROM fee_categories WHERE category_type = 'main'");
    $main_count = $count_stmt->fetchColumn();

    if ($main_count == 0) {
        // Add default main categories
        $default_categories = [
            ['শিক্ষা ফি', 'মাসিক বেতন, ভর্তি ফি এবং অন্যান্য শিক্ষা সংক্রান্ত ফি'],
            ['পরীক্ষা ফি', 'বিভিন্ন পরীক্ষার ফি এবং সার্টিফিকেট ফি'],
            ['অতিরিক্ত কার্যক্রম', 'খেলাধুলা, সাংস্কৃতিক কার্যক্রম এবং অন্যান্য কার্যক্রমের ফি'],
            ['সুবিধা ফি', 'লাইব্রেরি, ল্যাব এবং অন্যান্য সুবিধার ফি'],
            ['পরিবহন ফি', 'বাস ভাড়া এবং পরিবহন সংক্রান্ত ফি'],
            ['বিশেষ ফি', 'জরিমানা, বিলম্ব ফি এবং অন্যান্য বিশেষ ফি']
        ];

        $insert_stmt = $pdo->prepare("INSERT INTO fee_categories (category_name, category_type, description) VALUES (?, 'main', ?)");
        foreach ($default_categories as $category) {
            $insert_stmt->execute($category);
        }
    }
} catch(PDOException $e) {
    // Table might already exist
}

// Handle form submissions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'add_main_category') {
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);
            
            if (empty($category_name)) {
                throw new Exception('মূল ক্যাটেগরির নাম আবশ্যক।');
            }
            
            $stmt = $pdo->prepare("INSERT INTO fee_categories (category_name, category_type, description) VALUES (?, 'main', ?)");
            $stmt->execute([$category_name, $description]);
            
            $message = 'নতুন মূল ক্যাটেগরি সফলভাবে যোগ করা হয়েছে!';
            
        } elseif ($action === 'add_sub_category') {
            $category_name = trim($_POST['category_name']);
            $parent_id = (int)$_POST['parent_id'];
            $amount = (float)$_POST['amount'];
            $description = trim($_POST['description']);
            
            if (empty($category_name)) {
                throw new Exception('সাব ক্যাটেগরির নাম আবশ্যক।');
            }
            
            if ($parent_id <= 0) {
                throw new Exception('মূল ক্যাটেগরি নির্বাচন করুন।');
            }
            
            $stmt = $pdo->prepare("INSERT INTO fee_categories (category_name, category_type, parent_id, amount, description) VALUES (?, 'sub', ?, ?, ?)");
            $stmt->execute([$category_name, $parent_id, $amount, $description]);
            
            $message = 'নতুন সাব ক্যাটেগরি সফলভাবে যোগ করা হয়েছে!';
            
        } elseif ($action === 'edit_category') {
            $category_id = (int)$_POST['category_id'];
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);
            $is_active = isset($_POST['is_active']) ? 1 : 0;
            
            if (empty($category_name)) {
                throw new Exception('ক্যাটেগরির নাম আবশ্যক।');
            }
            
            // Check if it's a sub category to update amount
            $stmt = $pdo->prepare("SELECT category_type FROM fee_categories WHERE id = ?");
            $stmt->execute([$category_id]);
            $category = $stmt->fetch();
            
            if ($category['category_type'] === 'sub') {
                $amount = (float)$_POST['amount'];
                $stmt = $pdo->prepare("UPDATE fee_categories SET category_name = ?, amount = ?, description = ?, is_active = ? WHERE id = ?");
                $stmt->execute([$category_name, $amount, $description, $is_active, $category_id]);
            } else {
                $stmt = $pdo->prepare("UPDATE fee_categories SET category_name = ?, description = ?, is_active = ? WHERE id = ?");
                $stmt->execute([$category_name, $description, $is_active, $category_id]);
            }
            
            $message = 'ক্যাটেগরি সফলভাবে আপডেট করা হয়েছে!';
            
        } elseif ($action === 'delete_category') {
            $category_id = (int)$_POST['category_id'];
            
            // Check if it's a main category with sub categories
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM fee_categories WHERE parent_id = ?");
            $stmt->execute([$category_id]);
            $sub_count = $stmt->fetchColumn();
            
            if ($sub_count > 0) {
                throw new Exception('এই মূল ক্যাটেগরির অধীনে সাব ক্যাটেগরি রয়েছে। প্রথমে সাব ক্যাটেগরিগুলো মুছুন।');
            }
            
            $stmt = $pdo->prepare("DELETE FROM fee_categories WHERE id = ?");
            $stmt->execute([$category_id]);
            
            $message = 'ক্যাটেগরি সফলভাবে মুছে ফেলা হয়েছে!';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Fetch all categories
$stmt = $pdo->query("
    SELECT 
        mc.id as main_id,
        mc.category_name as main_name,
        mc.description as main_description,
        mc.is_active as main_active,
        mc.created_at as main_created,
        sc.id as sub_id,
        sc.category_name as sub_name,
        sc.amount as sub_amount,
        sc.description as sub_description,
        sc.is_active as sub_active,
        sc.created_at as sub_created
    FROM fee_categories mc
    LEFT JOIN fee_categories sc ON mc.id = sc.parent_id
    WHERE mc.category_type = 'main'
    ORDER BY mc.category_name, sc.category_name
");

$categories_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Organize data
$categories = [];
foreach ($categories_data as $row) {
    $main_id = $row['main_id'];
    
    if (!isset($categories[$main_id])) {
        $categories[$main_id] = [
            'id' => $row['main_id'],
            'name' => $row['main_name'],
            'description' => $row['main_description'],
            'is_active' => $row['main_active'],
            'created_at' => $row['main_created'],
            'sub_categories' => []
        ];
    }
    
    if ($row['sub_id']) {
        $categories[$main_id]['sub_categories'][] = [
            'id' => $row['sub_id'],
            'name' => $row['sub_name'],
            'amount' => $row['sub_amount'],
            'description' => $row['sub_description'],
            'is_active' => $row['sub_active'],
            'created_at' => $row['sub_created']
        ];
    }
}

// Get main categories for dropdown
$main_categories = $pdo->query("SELECT id, category_name FROM fee_categories WHERE category_type = 'main' AND is_active = 1 ORDER BY category_name")->fetchAll();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .fees-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
            color: white;
        }
        
        .main-category-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        
        .sub-category-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f7971e, #ffd200);
            color: white;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .categories-display {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-top: 2rem;
        }

        .categories-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .categories-header h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .category-group {
            margin-bottom: 2rem;
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            overflow: hidden;
        }

        .main-category-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .main-category-info h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        .main-category-info p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .category-actions {
            display: flex;
            gap: 0.5rem;
        }

        .sub-categories {
            padding: 1rem;
        }

        .sub-category-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 4px solid #f093fb;
        }

        .sub-category-info h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .sub-category-info p {
            margin: 0.3rem 0 0 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .sub-category-amount {
            font-size: 1.2rem;
            font-weight: 600;
            color: #28a745;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .fees-container {
                padding: 1rem;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .category-actions {
                flex-direction: column;
            }

            .sub-category-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .main-category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="fees-container">
        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div style="margin-bottom: 1rem;">
                    <a href="dashboard.php" class="btn btn-primary" style="background: rgba(255,255,255,0.2); border: 2px solid white;">
                        <i class="fas fa-arrow-left"></i> ড্যাশবোর্ডে ফিরুন
                    </a>
                </div>
                <h1><i class="fas fa-money-bill-wave"></i> ফি ব্যবস্থাপনা</h1>
                <p>মূল ক্যাটেগরি ও সাব ক্যাটেগরি তৈরি ও পরিচালনা করুন</p>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Dashboard Grid -->
            <div class="dashboard-grid">
                <!-- Add Main Category Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon main-category-icon">
                            <i class="fas fa-folder-plus"></i>
                        </div>
                        <h2 class="card-title">মূল ক্যাটেগরি যোগ করুন</h2>
                    </div>

                    <form method="POST" id="mainCategoryForm">
                        <input type="hidden" name="action" value="add_main_category">
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag"></i> ক্যাটেগরির নাম *
                            </label>
                            <input type="text" name="category_name" class="form-control" required 
                                   placeholder="যেমন: শিক্ষা ফি, পরীক্ষা ফি, অন্যান্য ফি">
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-align-left"></i> বিবরণ
                            </label>
                            <textarea name="description" class="form-control" rows="3" 
                                      placeholder="এই ক্যাটেগরির বিস্তারিত বিবরণ লিখুন"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-plus"></i> মূল ক্যাটেগরি যোগ করুন
                        </button>
                    </form>
                </div>

                <!-- Add Sub Category Card -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon sub-category-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <h2 class="card-title">সাব ক্যাটেগরি যোগ করুন</h2>
                    </div>

                    <form method="POST" id="subCategoryForm">
                        <input type="hidden" name="action" value="add_sub_category">
                        
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-folder"></i> মূল ক্যাটেগরি নির্বাচন *
                            </label>
                            <select name="parent_id" class="form-control" required>
                                <option value="">-- মূল ক্যাটেগরি নির্বাচন করুন --</option>
                                <?php foreach ($main_categories as $main_cat): ?>
                                    <option value="<?php echo $main_cat['id']; ?>">
                                        <?php echo htmlspecialchars($main_cat['category_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag"></i> সাব ক্যাটেগরির নাম *
                            </label>
                            <input type="text" name="category_name" class="form-control" required 
                                   placeholder="যেমন: মাসিক বেতন, ভর্তি ফি, বই ফি">
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-money-bill"></i> পরিমাণ (টাকা)
                            </label>
                            <input type="number" name="amount" class="form-control" step="0.01" min="0" 
                                   placeholder="0.00">
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-align-left"></i> বিবরণ
                            </label>
                            <textarea name="description" class="form-control" rows="2" 
                                      placeholder="এই সাব ক্যাটেগরির বিস্তারিত বিবরণ"></textarea>
                        </div>

                        <button type="submit" class="btn btn-success" style="width: 100%;">
                            <i class="fas fa-plus"></i> সাব ক্যাটেগরি যোগ করুন
                        </button>
                    </form>
                </div>
            </div>

            <!-- Categories Display -->
            <div class="categories-display">
                <div class="categories-header">
                    <h2><i class="fas fa-list"></i> ফি ক্যাটেগরি তালিকা</h2>
                    <p>সকল মূল ক্যাটেগরি ও তাদের সাব ক্যাটেগরিসমূহ</p>
                </div>

                <?php if (empty($categories)): ?>
                    <div class="empty-state">
                        <i class="fas fa-folder-open"></i>
                        <h3>কোন ক্যাটেগরি পাওয়া যায়নি</h3>
                        <p>প্রথমে একটি মূল ক্যাটেগরি যোগ করুন</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($categories as $main_category): ?>
                        <div class="category-group">
                            <div class="main-category-header">
                                <div class="main-category-info">
                                    <h3>
                                        <i class="fas fa-folder"></i>
                                        <?php echo htmlspecialchars($main_category['name']); ?>
                                        <span class="status-badge <?php echo $main_category['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $main_category['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                        </span>
                                    </h3>
                                    <?php if ($main_category['description']): ?>
                                        <p><?php echo htmlspecialchars($main_category['description']); ?></p>
                                    <?php endif; ?>
                                    <small>
                                        <i class="fas fa-calendar"></i>
                                        তৈরি: <?php echo date('d/m/Y', strtotime($main_category['created_at'])); ?>
                                        | সাব ক্যাটেগরি: <?php echo count($main_category['sub_categories']); ?>টি
                                    </small>
                                </div>
                                <div class="category-actions">
                                    <button onclick="editMainCategory(<?php echo $main_category['id']; ?>, '<?php echo addslashes($main_category['name']); ?>', '<?php echo addslashes($main_category['description']); ?>', <?php echo $main_category['is_active'] ? 'true' : 'false'; ?>)"
                                            class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i> সম্পাদনা
                                    </button>
                                    <button onclick="deleteCategory(<?php echo $main_category['id']; ?>, '<?php echo addslashes($main_category['name']); ?>')"
                                            class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i> মুছুন
                                    </button>
                                </div>
                            </div>

                            <div class="sub-categories">
                                <?php if (empty($main_category['sub_categories'])): ?>
                                    <div style="text-align: center; padding: 2rem; color: #6c757d;">
                                        <i class="fas fa-plus-circle" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                                        <p>এই ক্যাটেগরিতে কোন সাব ক্যাটেগরি নেই</p>
                                        <small>উপরের ফর্ম ব্যবহার করে সাব ক্যাটেগরি যোগ করুন</small>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($main_category['sub_categories'] as $sub_category): ?>
                                        <div class="sub-category-item">
                                            <div class="sub-category-info">
                                                <h4>
                                                    <i class="fas fa-file"></i>
                                                    <?php echo htmlspecialchars($sub_category['name']); ?>
                                                    <span class="status-badge <?php echo $sub_category['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                                        <?php echo $sub_category['is_active'] ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                                    </span>
                                                </h4>
                                                <?php if ($sub_category['description']): ?>
                                                    <p><?php echo htmlspecialchars($sub_category['description']); ?></p>
                                                <?php endif; ?>
                                                <small>
                                                    <i class="fas fa-calendar"></i>
                                                    তৈরি: <?php echo date('d/m/Y', strtotime($sub_category['created_at'])); ?>
                                                </small>
                                            </div>
                                            <div style="display: flex; align-items: center; gap: 1rem;">
                                                <div class="sub-category-amount">
                                                    <i class="fas fa-taka-sign"></i>
                                                    <?php echo number_format($sub_category['amount'], 2); ?> টাকা
                                                </div>
                                                <div class="category-actions">
                                                    <button onclick="editSubCategory(<?php echo $sub_category['id']; ?>, '<?php echo addslashes($sub_category['name']); ?>', '<?php echo addslashes($sub_category['description']); ?>', <?php echo $sub_category['amount']; ?>, <?php echo $sub_category['is_active'] ? 'true' : 'false'; ?>)"
                                                            class="btn btn-warning btn-sm">
                                                        <i class="fas fa-edit"></i> সম্পাদনা
                                                    </button>
                                                    <button onclick="deleteCategory(<?php echo $sub_category['id']; ?>, '<?php echo addslashes($sub_category['name']); ?>')"
                                                            class="btn btn-danger btn-sm">
                                                        <i class="fas fa-trash"></i> মুছুন
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">ক্যাটেগরি সম্পাদনা</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="edit_category">
                <input type="hidden" name="category_id" id="editCategoryId">

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-tag"></i> ক্যাটেগরির নাম *
                    </label>
                    <input type="text" name="category_name" id="editCategoryName" class="form-control" required>
                </div>

                <div class="form-group" id="editAmountGroup" style="display: none;">
                    <label class="form-label">
                        <i class="fas fa-money-bill"></i> পরিমাণ (টাকা)
                    </label>
                    <input type="number" name="amount" id="editAmount" class="form-control" step="0.01" min="0">
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-align-left"></i> বিবরণ
                    </label>
                    <textarea name="description" id="editDescription" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                        <input type="checkbox" name="is_active" id="editIsActive" value="1">
                        <span class="form-label" style="margin: 0;">সক্রিয় অবস্থা</span>
                    </label>
                </div>

                <div style="display: flex; gap: 1rem;">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        <i class="fas fa-save"></i> সংরক্ষণ করুন
                    </button>
                    <button type="button" onclick="closeModal()" class="btn btn-danger" style="flex: 1;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function editMainCategory(id, name, description, isActive) {
            document.getElementById('modalTitle').textContent = 'মূল ক্যাটেগরি সম্পাদনা';
            document.getElementById('editCategoryId').value = id;
            document.getElementById('editCategoryName').value = name;
            document.getElementById('editDescription').value = description;
            document.getElementById('editIsActive').checked = isActive;
            document.getElementById('editAmountGroup').style.display = 'none';
            document.getElementById('editModal').style.display = 'block';
        }

        function editSubCategory(id, name, description, amount, isActive) {
            document.getElementById('modalTitle').textContent = 'সাব ক্যাটেগরি সম্পাদনা';
            document.getElementById('editCategoryId').value = id;
            document.getElementById('editCategoryName').value = name;
            document.getElementById('editDescription').value = description;
            document.getElementById('editAmount').value = amount;
            document.getElementById('editIsActive').checked = isActive;
            document.getElementById('editAmountGroup').style.display = 'block';
            document.getElementById('editModal').style.display = 'block';
        }

        function deleteCategory(id, name) {
            if (confirm('আপনি কি নিশ্চিত যে "' + name + '" ক্যাটেগরিটি মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_category">
                    <input type="hidden" name="category_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target == modal) {
                closeModal();
            }
        }

        // Form reset after submission
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                if (form.id === 'mainCategoryForm' || form.id === 'subCategoryForm') {
                    form.addEventListener('submit', function() {
                        setTimeout(() => {
                            if (!document.querySelector('.alert-danger')) {
                                form.reset();
                            }
                        }, 100);
                    });
                }
            });
        });
    </script>
</body>
</html>
