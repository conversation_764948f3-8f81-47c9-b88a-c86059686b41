<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Classes Table Structure Check</h2>";
    
    // Check table structure
    echo "<h3>Current Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE classes");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    $has_academic_year = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'academic_year') {
            $has_academic_year = true;
        }
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (!$has_academic_year) {
        echo "<h3 style='color: red;'>❌ academic_year column is missing!</h3>";
        echo "<p>Adding academic_year column now...</p>";
        
        try {
            // Add the column
            $pdo->exec("ALTER TABLE classes ADD COLUMN academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "'");
            echo "<p style='color: green;'>✅ academic_year column added successfully!</p>";
            
            // Update existing records
            $pdo->exec("UPDATE classes SET academic_year = '" . date('Y') . "' WHERE academic_year = '' OR academic_year IS NULL");
            echo "<p style='color: green;'>✅ Existing records updated!</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding column: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<h3 style='color: green;'>✅ academic_year column exists!</h3>";
    }
    
    // Show current data
    echo "<h3>Current Classes Data:</h3>";
    $stmt = $pdo->query("SELECT * FROM classes LIMIT 10");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($classes)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #e8f5e8;'>";
        foreach (array_keys($classes[0]) as $header) {
            echo "<th style='padding: 10px;'>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        foreach ($classes as $class) {
            echo "<tr>";
            foreach ($class as $value) {
                echo "<td style='padding: 8px;'>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>No classes found in the database.</p>";
        
        // Add sample classes
        echo "<h3>Adding Sample Classes:</h3>";
        $sample_classes = [
            ['প্রথম শ্রেণী', 'ক', date('Y')],
            ['দ্বিতীয় শ্রেণী', 'ক', date('Y')],
            ['তৃতীয় শ্রেণী', 'ক', date('Y')],
            ['চতুর্থ শ্রেণী', 'ক', date('Y')],
            ['পঞ্চম শ্রেণী', 'ক', date('Y')]
        ];
        
        try {
            $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year, status) VALUES (?, ?, ?, 'active')");
            
            foreach ($sample_classes as $class) {
                $stmt->execute($class);
                echo "<p style='color: green;'>✅ {$class[0]} - {$class[1]} added</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding classes: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test the problematic queries
    echo "<h3>Testing Queries:</h3>";
    
    try {
        echo "<p><strong>Query 1:</strong> SELECT id, class_name, section, academic_year FROM classes WHERE status = 'active'</p>";
        $stmt = $pdo->query("SELECT id, class_name, section, academic_year FROM classes WHERE status = 'active' ORDER BY class_name, section");
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p style='color: green;'>✅ Query 1 successful - " . count($result) . " rows returned</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query 1 failed: " . $e->getMessage() . "</p>";
    }
    
    try {
        echo "<p><strong>Query 2:</strong> SELECT DISTINCT academic_year FROM classes</p>";
        $stmt = $pdo->query("SELECT DISTINCT academic_year FROM classes ORDER BY academic_year DESC");
        $result = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p style='color: green;'>✅ Query 2 successful - Years: " . implode(', ', $result) . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query 2 failed: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px;">
    <a href="class_subjects.php" style="display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        🔗 ক্লাস-বিষয় সংযোগে ফিরে যান
    </a>
</div>
