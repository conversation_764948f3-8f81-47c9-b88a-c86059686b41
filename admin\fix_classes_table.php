<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Classes Table Fix</h2>";
    
    // Check if academic_year column exists
    $stmt = $pdo->query("DESCRIBE classes");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('academic_year', $columns)) {
        echo "<p>Adding academic_year column to classes table...</p>";
        
        // Add academic_year column
        $sql = "ALTER TABLE classes ADD COLUMN academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "'";
        $pdo->exec($sql);
        
        echo "<p style='color: green;'>✅ academic_year column added successfully!</p>";
        
        // Update existing classes with current year
        $stmt = $pdo->prepare("UPDATE classes SET academic_year = ? WHERE academic_year = ''");
        $stmt->execute([date('Y')]);
        
        echo "<p style='color: green;'>✅ Existing classes updated with current year!</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ academic_year column already exists.</p>";
    }
    
    // Show current table structure
    echo "<h3>Current Classes Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE classes");
    $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample classes data
    echo "<h3>Sample Classes Data:</h3>";
    $stmt = $pdo->query("SELECT * FROM classes LIMIT 5");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($classes)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #e8f5e8;'>";
        foreach (array_keys($classes[0]) as $header) {
            echo "<th style='padding: 10px;'>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        foreach ($classes as $class) {
            echo "<tr>";
            foreach ($class as $value) {
                echo "<td style='padding: 8px;'>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No classes found. Please add some classes first.</p>";
    }
    
    // Add some sample classes if none exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM classes");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "<h3>Adding Sample Classes:</h3>";
        
        $sample_classes = [
            ['প্রথম শ্রেণী', 'ক', date('Y')],
            ['দ্বিতীয় শ্রেণী', 'ক', date('Y')],
            ['তৃতীয় শ্রেণী', 'ক', date('Y')],
            ['চতুর্থ শ্রেণী', 'ক', date('Y')],
            ['পঞ্চম শ্রেণী', 'ক', date('Y')],
            ['ষষ্ঠ শ্রেণী', 'ক', date('Y')],
            ['সপ্তম শ্রেণী', 'ক', date('Y')],
            ['অষ্টম শ্রেণী', 'ক', date('Y')],
            ['নবম শ্রেণী', 'ক', date('Y')],
            ['দশম শ্রেণী', 'ক', date('Y')]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year, status) VALUES (?, ?, ?, 'active')");
        
        foreach ($sample_classes as $class) {
            $stmt->execute($class);
            echo "<p style='color: green;'>✅ {$class[0]} - {$class[1]} যোগ করা হয়েছে</p>";
        }
    }
    
    echo "<br><h3>✅ Classes table is now ready for class-subject management!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px;">
    <a href="class_subjects.php" style="display: inline-block; padding: 15px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        🔗 ক্লাস-বিষয় সংযোগে ফিরে যান
    </a>
</div>

<script>
// Auto redirect after 3 seconds
setTimeout(function() {
    window.location.href = 'class_subjects.php';
}, 3000);
</script>
