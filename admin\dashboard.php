<?php
require_once '../config/database.php';
checkRole('admin');

$db = getDB();

// ড্যাশবোর্ড স্ট্যাটিস্টিক্স
$total_students = $db->count('students', 'status = ?', ['active']);
$total_teachers = $db->count('teachers', 'status = ?', ['active']);
$total_classes = $db->count('classes', 'status = ?', ['active']);
$total_subjects = $db->count('subjects', 'status = ?', ['active']);

// সাম্প্রতিক কার্যক্রম
$recent_students = $db->select("
    SELECT s.*, c.class_name, c.section 
    FROM students s 
    JOIN classes c ON s.class_id = c.id 
    ORDER BY s.created_at DESC 
    LIMIT 5
");

$recent_notices = $db->select("
    SELECT n.*, u.username as published_by_name 
    FROM notices n 
    JOIN users u ON n.published_by = u.id 
    WHERE n.status = 'published' 
    ORDER BY n.created_at DESC 
    LIMIT 5
");
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অ্যাডমিন ড্যাশবোর্ড - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="logo">স্কুল ব্যবস্থাপনা সিস্টেম</div>
            <ul class="nav-links">
                <li><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                <li><a href="students.php">ছাত্র</a></li>
                <li><a href="teachers.php">শিক্ষক</a></li>
                <li><a href="classes.php">ক্লাস</a></li>
                <li><a href="subjects.php">বিষয়</a></li>
                <li><a href="attendance.php">উপস্থিতি</a></li>
                <li><a href="marks.php">নম্বর</a></li>
                <li><a href="notices.php">নোটিশ</a></li>
                <li><a href="../logout.php">লগআউট</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main Content -->
    <div class="container">
        <h1>অ্যাডমিন ড্যাশবোর্ড</h1>
        <p>স্বাগতম, <?php echo $_SESSION['username']; ?>!</p>

        <!-- Statistics Cards -->
        <div class="dashboard-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_students; ?></div>
                <div class="stat-label">মোট ছাত্র</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_teachers; ?></div>
                <div class="stat-label">মোট শিক্ষক</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_classes; ?></div>
                <div class="stat-label">মোট ক্লাস</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_subjects; ?></div>
                <div class="stat-label">মোট বিষয়</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">দ্রুত কার্যক্রম</h2>
            </div>
            <div class="d-flex" style="gap: 1rem; flex-wrap: wrap;">
                <a href="students.php?action=add" class="btn btn-primary">নতুন ছাত্র যোগ করুন</a>
                <a href="teachers.php?action=add" class="btn btn-success">নতুন শিক্ষক যোগ করুন</a>
                <a href="classes.php?action=add" class="btn btn-warning">নতুন ক্লাস যোগ করুন</a>
                <a href="notices.php?action=add" class="btn btn-primary">নতুন নোটিশ যোগ করুন</a>
            </div>
        </div>

        <!-- Recent Students -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">সাম্প্রতিক ছাত্র</h2>
            </div>
            <?php if (!empty($recent_students)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ছাত্র ID</th>
                            <th>নাম</th>
                            <th>ক্লাস</th>
                            <th>রোল নম্বর</th>
                            <th>যোগদানের তারিখ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_students as $student): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                <td><?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?></td>
                                <td><?php echo htmlspecialchars($student['roll_number']); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($student['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="text-center">
                    <a href="students.php" class="btn btn-primary">সব ছাত্র দেখুন</a>
                </div>
            <?php else: ?>
                <p>কোন ছাত্র পাওয়া যায়নি।</p>
            <?php endif; ?>
        </div>

        <!-- Recent Notices -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">সাম্প্রতিক নোটিশ</h2>
            </div>
            <?php if (!empty($recent_notices)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>শিরোনাম</th>
                            <th>লক্ষ্য দর্শক</th>
                            <th>প্রকাশকারী</th>
                            <th>প্রকাশের তারিখ</th>
                            <th>অগ্রাধিকার</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_notices as $notice): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($notice['title']); ?></td>
                                <td>
                                    <?php 
                                    switch($notice['target_audience']) {
                                        case 'all': echo 'সবার জন্য'; break;
                                        case 'teachers': echo 'শিক্ষকদের জন্য'; break;
                                        case 'students': echo 'ছাত্রদের জন্য'; break;
                                        case 'specific_class': echo 'নির্দিষ্ট ক্লাস'; break;
                                    }
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($notice['published_by_name']); ?></td>
                                <td><?php echo date('d/m/Y', strtotime($notice['publish_date'])); ?></td>
                                <td>
                                    <span class="badge badge-<?php echo $notice['priority']; ?>">
                                        <?php 
                                        switch($notice['priority']) {
                                            case 'high': echo 'উচ্চ'; break;
                                            case 'medium': echo 'মধ্যম'; break;
                                            case 'low': echo 'নিম্ন'; break;
                                        }
                                        ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="text-center">
                    <a href="notices.php" class="btn btn-primary">সব নোটিশ দেখুন</a>
                </div>
            <?php else: ?>
                <p>কোন নোটিশ পাওয়া যায়নি।</p>
            <?php endif; ?>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
</body>
</html>
