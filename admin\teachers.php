<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_teacher') {
        try {
            $teacher_id = trim($_POST['teacher_id']);
            $first_name = trim($_POST['first_name']);
            $last_name = trim($_POST['last_name']);
            $email = trim($_POST['email']);
            $phone = trim($_POST['phone']);
            $address = trim($_POST['address']);
            $qualification = trim($_POST['qualification']);
            $subject_specialization = trim($_POST['subject_specialization']);
            $joining_date = $_POST['joining_date'];
            $salary = (float)$_POST['salary'];
            $status = $_POST['status'];
            
            // Validate required fields
            if (empty($teacher_id) || empty($first_name) || empty($last_name)) {
                throw new Exception('শিক্ষক ID, নাম এবং পদবি আবশ্যক।');
            }
            
            // Check if teacher ID already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM teachers WHERE teacher_id = ?");
            $stmt->execute([$teacher_id]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('এই শিক্ষক ID ইতিমধ্যে ব্যবহৃত হয়েছে।');
            }
            
            // Check if email already exists
            if (!empty($email)) {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM teachers WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('এই ইমেইল ইতিমধ্যে ব্যবহৃত হয়েছে।');
                }
            }
            
            // Handle photo upload
            $photo_path = '';
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../uploads/teachers/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $file_extension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
                $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                
                if (!in_array($file_extension, $allowed_extensions)) {
                    throw new Exception('শুধুমাত্র JPG, JPEG, PNG, GIF ফাইল আপলোড করা যাবে।');
                }
                
                if ($_FILES['photo']['size'] > 2 * 1024 * 1024) { // 2MB
                    throw new Exception('ফাইলের সাইজ ২ MB এর বেশি হতে পারবে না।');
                }
                
                $new_filename = $teacher_id . '_' . time() . '.' . $file_extension;
                $photo_path = 'uploads/teachers/' . $new_filename;
                
                if (!move_uploaded_file($_FILES['photo']['tmp_name'], '../' . $photo_path)) {
                    throw new Exception('ছবি আপলোড করতে সমস্যা হয়েছে।');
                }
            }
            
            // Insert teacher
            $stmt = $pdo->prepare("
                INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, address, 
                                    qualification, subject_specialization, joining_date, salary, status, photo)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $teacher_id, $first_name, $last_name, $email, $phone, $address,
                $qualification, $subject_specialization, $joining_date, $salary, $status, $photo_path
            ]);
            
            $message = 'শিক্ষক সফলভাবে যোগ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'update_teacher') {
        try {
            $id = (int)$_POST['id'];
            $first_name = trim($_POST['first_name']);
            $last_name = trim($_POST['last_name']);
            $email = trim($_POST['email']);
            $phone = trim($_POST['phone']);
            $address = trim($_POST['address']);
            $qualification = trim($_POST['qualification']);
            $subject_specialization = trim($_POST['subject_specialization']);
            $salary = (float)$_POST['salary'];
            $status = $_POST['status'];
            
            // Validate required fields
            if (empty($first_name) || empty($last_name)) {
                throw new Exception('নাম এবং পদবি আবশ্যক।');
            }
            
            // Check if email already exists for other teachers
            if (!empty($email)) {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM teachers WHERE email = ? AND id != ?");
                $stmt->execute([$email, $id]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('এই ইমেইল অন্য শিক্ষক ব্যবহার করছেন।');
                }
            }
            
            // Update teacher
            $stmt = $pdo->prepare("
                UPDATE teachers 
                SET first_name = ?, last_name = ?, email = ?, phone = ?, address = ?, 
                    qualification = ?, subject_specialization = ?, salary = ?, status = ?
                WHERE id = ?
            ");
            $stmt->execute([
                $first_name, $last_name, $email, $phone, $address,
                $qualification, $subject_specialization, $salary, $status, $id
            ]);
            
            $message = 'শিক্ষকের তথ্য সফলভাবে আপডেট করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'delete_teacher') {
        try {
            $id = (int)$_POST['id'];
            
            // Get teacher info for photo deletion
            $stmt = $pdo->prepare("SELECT photo FROM teachers WHERE id = ?");
            $stmt->execute([$id]);
            $teacher = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Delete teacher
            $stmt = $pdo->prepare("DELETE FROM teachers WHERE id = ?");
            $stmt->execute([$id]);
            
            // Delete photo file if exists
            if ($teacher && $teacher['photo'] && file_exists('../' . $teacher['photo'])) {
                unlink('../' . $teacher['photo']);
            }
            
            $message = 'শিক্ষক সফলভাবে মুছে ফেলা হয়েছে!';
            
        } catch (Exception $e) {
            $error = 'শিক্ষক মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        }
    }
}

// Get search and filter parameters
$search_name = $_GET['search_name'] ?? '';
$search_teacher_id = $_GET['search_teacher_id'] ?? '';
$search_subject = $_GET['search_subject'] ?? '';
$search_status = $_GET['search_status'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'first_name';
$sort_order = $_GET['sort_order'] ?? 'ASC';

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($search_name)) {
    $where_conditions[] = "(first_name LIKE ? OR last_name LIKE ? OR CONCAT(first_name, ' ', last_name) LIKE ?)";
    $search_term = "%$search_name%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($search_teacher_id)) {
    $where_conditions[] = "teacher_id LIKE ?";
    $params[] = "%$search_teacher_id%";
}

if (!empty($search_subject)) {
    $where_conditions[] = "subject_specialization LIKE ?";
    $params[] = "%$search_subject%";
}

if (!empty($search_status)) {
    $where_conditions[] = "status = ?";
    $params[] = $search_status;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// Validate sort parameters
$allowed_sort_columns = ['first_name', 'last_name', 'teacher_id', 'subject_specialization', 'joining_date', 'salary'];
if (!in_array($sort_by, $allowed_sort_columns)) {
    $sort_by = 'first_name';
}
$sort_order = strtoupper($sort_order) === 'DESC' ? 'DESC' : 'ASC';

// Get teachers
try {
    $sql = "SELECT * FROM teachers $where_clause ORDER BY $sort_by $sort_order";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM teachers $where_clause";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_teachers = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
} catch (PDOException $e) {
    $teachers = [];
    $total_teachers = 0;
}

// Generate next teacher ID
$next_teacher_id = 'TCH' . str_pad((count($teachers) + 1), 3, '0', STR_PAD_LEFT);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .teachers-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .section-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }
        
        .form-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .form-grid-full {
            grid-column: 1 / -1;
        }
        
        .search-section {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .search-toggle {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .teachers-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .teachers-table th {
            background: #667eea;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .teachers-table td {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .teachers-table tr:hover {
            background: #f8f9fa;
        }
        
        .teacher-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .action-btn {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
            margin-right: 0.5rem;
        }
        
        .btn-edit {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
        
        .photo-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">👩‍🏫 শিক্ষক ব্যবস্থাপনা</h1>
                <p class="content-subtitle">সকল শিক্ষকের তথ্য ব্যবস্থাপনা করুন</p>
            </div>

            <div class="content-body">
                <div class="teachers-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Add New Teacher Button -->
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <button onclick="openAddModal()" class="submit-btn">
                            ➕ নতুন শিক্ষক যোগ করুন
                        </button>
                    </div>

                    <!-- Search Section -->
                    <div class="search-section">
                        <div class="search-header">
                            <h3 style="color: #2c3e50; margin: 0;">🔍 শিক্ষক খুঁজুন</h3>
                            <button type="button" class="search-toggle" onclick="toggleSearch()">
                                সার্চ অপশন
                            </button>
                        </div>

                        <div id="searchForm" style="display: <?php echo (!empty($search_name) || !empty($search_teacher_id) || !empty($search_subject) || !empty($search_status)) ? 'block' : 'none'; ?>;">
                            <form method="GET" class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">নাম দিয়ে সার্চ</label>
                                    <input type="text" name="search_name" class="form-control"
                                           value="<?php echo htmlspecialchars($search_name); ?>"
                                           placeholder="নাম বা পদবি লিখুন">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">শিক্ষক ID</label>
                                    <input type="text" name="search_teacher_id" class="form-control"
                                           value="<?php echo htmlspecialchars($search_teacher_id); ?>"
                                           placeholder="TCH001">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">বিষয়</label>
                                    <input type="text" name="search_subject" class="form-control"
                                           value="<?php echo htmlspecialchars($search_subject); ?>"
                                           placeholder="বিষয়ের নাম">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">স্ট্যাটাস</label>
                                    <select name="search_status" class="form-control">
                                        <option value="">সব স্ট্যাটাস</option>
                                        <option value="active" <?php echo ($search_status == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                        <option value="inactive" <?php echo ($search_status == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">সাজানোর ভিত্তি</label>
                                    <select name="sort_by" class="form-control">
                                        <option value="first_name" <?php echo ($sort_by == 'first_name') ? 'selected' : ''; ?>>নাম</option>
                                        <option value="teacher_id" <?php echo ($sort_by == 'teacher_id') ? 'selected' : ''; ?>>শিক্ষক ID</option>
                                        <option value="subject_specialization" <?php echo ($sort_by == 'subject_specialization') ? 'selected' : ''; ?>>বিষয়</option>
                                        <option value="joining_date" <?php echo ($sort_by == 'joining_date') ? 'selected' : ''; ?>>যোগদানের তারিখ</option>
                                        <option value="salary" <?php echo ($sort_by == 'salary') ? 'selected' : ''; ?>>বেতন</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">সাজানোর ক্রম</label>
                                    <select name="sort_order" class="form-control">
                                        <option value="ASC" <?php echo ($sort_order == 'ASC') ? 'selected' : ''; ?>>ছোট থেকে বড়</option>
                                        <option value="DESC" <?php echo ($sort_order == 'DESC') ? 'selected' : ''; ?>>বড় থেকে ছোট</option>
                                    </select>
                                </div>

                                <div class="form-grid-full" style="text-align: center; margin-top: 1rem;">
                                    <button type="submit" class="action-btn" style="background: #28a745; color: white; padding: 0.75rem 1.5rem;">
                                        🔍 সার্চ করুন
                                    </button>
                                    <a href="?" class="action-btn" style="background: #6c757d; color: white; padding: 0.75rem 1.5rem; text-decoration: none; margin-left: 1rem;">
                                        🔄 রিসেট করুন
                                    </a>
                                </div>
                            </form>
                        </div>

                        <!-- Search Statistics -->
                        <div style="text-align: center; margin-top: 1rem; padding: 1rem; background: #e9ecef; border-radius: 6px;">
                            📊 মোট <?php echo $total_teachers; ?> জন শিক্ষক পাওয়া গেছে
                        </div>
                    </div>

                    <!-- Teachers Table -->
                    <div class="form-section">
                        <h3 class="form-title">
                            📋 শিক্ষকদের তালিকা
                        </h3>

                        <?php if (empty($teachers)): ?>
                            <div style="text-align: center; padding: 3rem; color: #6c757d;">
                                <h4>👩‍🏫 কোন শিক্ষক পাওয়া যায়নি</h4>
                                <p>নতুন শিক্ষক যোগ করুন বা সার্চ ফিল্টার পরিবর্তন করুন।</p>
                            </div>
                        <?php else: ?>
                            <div style="overflow-x: auto;">
                                <table class="teachers-table">
                                    <thead>
                                        <tr>
                                            <th>ছবি</th>
                                            <th>
                                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'teacher_id', 'sort_order' => ($sort_by == 'teacher_id' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                                   style="color: white; text-decoration: none;">
                                                    শিক্ষক ID
                                                    <?php if ($sort_by == 'teacher_id'): ?>
                                                        <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                                    <?php endif; ?>
                                                </a>
                                            </th>
                                            <th>
                                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'first_name', 'sort_order' => ($sort_by == 'first_name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                                   style="color: white; text-decoration: none;">
                                                    নাম
                                                    <?php if ($sort_by == 'first_name'): ?>
                                                        <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                                    <?php endif; ?>
                                                </a>
                                            </th>
                                            <th>যোগাযোগ</th>
                                            <th>
                                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'subject_specialization', 'sort_order' => ($sort_by == 'subject_specialization' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                                   style="color: white; text-decoration: none;">
                                                    বিষয়
                                                    <?php if ($sort_by == 'subject_specialization'): ?>
                                                        <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                                    <?php endif; ?>
                                                </a>
                                            </th>
                                            <th>
                                                <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'salary', 'sort_order' => ($sort_by == 'salary' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                                   style="color: white; text-decoration: none;">
                                                    বেতন
                                                    <?php if ($sort_by == 'salary'): ?>
                                                        <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                                    <?php endif; ?>
                                                </a>
                                            </th>
                                            <th>স্ট্যাটাস</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($teachers as $teacher): ?>
                                            <tr>
                                                <td>
                                                    <?php if (isset($teacher['photo']) && $teacher['photo']): ?>
                                                        <img src="../<?php echo htmlspecialchars($teacher['photo']); ?>"
                                                             alt="ছবি" class="teacher-photo">
                                                    <?php else: ?>
                                                        <div style="width: 50px; height: 50px; background: #dee2e6; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                            👩‍🏫
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($teacher['teacher_id'] ?? ''); ?></strong>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars(($teacher['first_name'] ?? '') . ' ' . ($teacher['last_name'] ?? '')); ?></strong>
                                                </td>
                                                <td>
                                                    <?php if (isset($teacher['email']) && $teacher['email']): ?>
                                                        📧 <?php echo htmlspecialchars($teacher['email']); ?><br>
                                                    <?php endif; ?>
                                                    <?php if (isset($teacher['phone']) && $teacher['phone']): ?>
                                                        📱 <?php echo htmlspecialchars($teacher['phone']); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (isset($teacher['subject_specialization']) && $teacher['subject_specialization']): ?>
                                                        <span style="background: #e3f2fd; color: #1976d2; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.85rem;">
                                                            <?php echo htmlspecialchars($teacher['subject_specialization']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span style="color: #6c757d;">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (isset($teacher['salary']) && $teacher['salary']): ?>
                                                        <strong>৳<?php echo number_format($teacher['salary']); ?></strong>
                                                    <?php else: ?>
                                                        <span style="color: #6c757d;">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php $status = $teacher['status'] ?? 'active'; ?>
                                                    <span class="status-badge status-<?php echo $status; ?>">
                                                        <?php echo $status == 'active' ? '✅ সক্রিয়' : '❌ নিষ্ক্রিয়'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <button onclick="openEditModal(<?php echo htmlspecialchars(json_encode($teacher)); ?>)"
                                                            class="action-btn btn-edit">
                                                        ✏️ সম্পাদনা
                                                    </button>
                                                    <button onclick="deleteTeacher(<?php echo $teacher['id']; ?>)"
                                                            class="action-btn btn-delete">
                                                        🗑️ মুছুন
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Teacher Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddModal()">&times;</span>
            <h2 style="color: #2c3e50; margin-bottom: 1.5rem;">➕ নতুন শিক্ষক যোগ করুন</h2>

            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="add_teacher">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">শিক্ষক ID *</label>
                        <input type="text" name="teacher_id" class="form-control"
                               value="<?php echo $next_teacher_id; ?>" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">নাম *</label>
                        <input type="text" name="first_name" class="form-control"
                               placeholder="নাম লিখুন" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">পদবি *</label>
                        <input type="text" name="last_name" class="form-control"
                               placeholder="পদবি লিখুন" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">ইমেইল</label>
                        <input type="email" name="email" class="form-control"
                               placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label class="form-label">ফোন নম্বর</label>
                        <input type="tel" name="phone" class="form-control"
                               placeholder="01XXXXXXXXX">
                    </div>

                    <div class="form-group">
                        <label class="form-label">যোগদানের তারিখ</label>
                        <input type="date" name="joining_date" class="form-control"
                               value="<?php echo date('Y-m-d'); ?>">
                    </div>

                    <div class="form-group">
                        <label class="form-label">শিক্ষাগত যোগ্যতা</label>
                        <input type="text" name="qualification" class="form-control"
                               placeholder="যেমন: এমএ (বাংলা)">
                    </div>

                    <div class="form-group">
                        <label class="form-label">বিষয় বিশেষজ্ঞতা</label>
                        <input type="text" name="subject_specialization" class="form-control"
                               placeholder="যেমন: বাংলা, গণিত">
                    </div>

                    <div class="form-group">
                        <label class="form-label">বেতন (টাকা)</label>
                        <input type="number" name="salary" class="form-control"
                               placeholder="25000" min="0" step="100">
                    </div>

                    <div class="form-group">
                        <label class="form-label">স্ট্যাটাস</label>
                        <select name="status" class="form-control">
                            <option value="active">সক্রিয়</option>
                            <option value="inactive">নিষ্ক্রিয়</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">ছবি</label>
                        <input type="file" name="photo" class="form-control"
                               accept="image/*" onchange="previewImage(this, 'addPreview')">
                        <div id="addPreview"></div>
                    </div>

                    <div class="form-group form-grid-full">
                        <label class="form-label">ঠিকানা</label>
                        <textarea name="address" class="form-control" rows="3"
                                  placeholder="সম্পূর্ণ ঠিকানা লিখুন"></textarea>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="submit" class="submit-btn">
                        ✅ শিক্ষক যোগ করুন
                    </button>
                    <button type="button" onclick="closeAddModal()"
                            style="background: #6c757d; color: white; border: none; padding: 1rem 2rem; border-radius: 8px; margin-left: 1rem;">
                        ❌ বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Teacher Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h2 style="color: #2c3e50; margin-bottom: 1.5rem;">✏️ শিক্ষকের তথ্য সম্পাদনা</h2>

            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_teacher">
                <input type="hidden" name="id" id="edit_id">

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">শিক্ষক ID</label>
                        <input type="text" id="edit_teacher_id" class="form-control" readonly
                               style="background: #e9ecef;">
                    </div>

                    <div class="form-group">
                        <label class="form-label">নাম *</label>
                        <input type="text" name="first_name" id="edit_first_name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">পদবি *</label>
                        <input type="text" name="last_name" id="edit_last_name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">ইমেইল</label>
                        <input type="email" name="email" id="edit_email" class="form-control">
                    </div>

                    <div class="form-group">
                        <label class="form-label">ফোন নম্বর</label>
                        <input type="tel" name="phone" id="edit_phone" class="form-control">
                    </div>

                    <div class="form-group">
                        <label class="form-label">যোগদানের তারিখ</label>
                        <input type="date" id="edit_joining_date" class="form-control" readonly
                               style="background: #e9ecef;">
                    </div>

                    <div class="form-group">
                        <label class="form-label">শিক্ষাগত যোগ্যতা</label>
                        <input type="text" name="qualification" id="edit_qualification" class="form-control">
                    </div>

                    <div class="form-group">
                        <label class="form-label">বিষয় বিশেষজ্ঞতা</label>
                        <input type="text" name="subject_specialization" id="edit_subject_specialization" class="form-control">
                    </div>

                    <div class="form-group">
                        <label class="form-label">বেতন (টাকা)</label>
                        <input type="number" name="salary" id="edit_salary" class="form-control" min="0" step="100">
                    </div>

                    <div class="form-group">
                        <label class="form-label">স্ট্যাটাস</label>
                        <select name="status" id="edit_status" class="form-control">
                            <option value="active">সক্রিয়</option>
                            <option value="inactive">নিষ্ক্রিয়</option>
                        </select>
                    </div>

                    <div class="form-group form-grid-full">
                        <label class="form-label">ঠিকানা</label>
                        <textarea name="address" id="edit_address" class="form-control" rows="3"></textarea>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="submit" class="submit-btn">
                        ✅ আপডেট করুন
                    </button>
                    <button type="button" onclick="closeEditModal()"
                            style="background: #6c757d; color: white; border: none; padding: 1rem 2rem; border-radius: 8px; margin-left: 1rem;">
                        ❌ বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Modal functions
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        function closeAddModal() {
            document.getElementById('addModal').style.display = 'none';
        }

        function openEditModal(teacher) {
            document.getElementById('edit_id').value = teacher.id || '';
            document.getElementById('edit_teacher_id').value = teacher.teacher_id || '';
            document.getElementById('edit_first_name').value = teacher.first_name || '';
            document.getElementById('edit_last_name').value = teacher.last_name || '';
            document.getElementById('edit_email').value = teacher.email || '';
            document.getElementById('edit_phone').value = teacher.phone || '';
            document.getElementById('edit_joining_date').value = teacher.joining_date || '';
            document.getElementById('edit_qualification').value = teacher.qualification || '';
            document.getElementById('edit_subject_specialization').value = teacher.subject_specialization || '';
            document.getElementById('edit_salary').value = teacher.salary || '';
            document.getElementById('edit_status').value = teacher.status || 'active';
            document.getElementById('edit_address').value = teacher.address || '';

            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Delete teacher function
        function deleteTeacher(id) {
            if (confirm('এই শিক্ষককে মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_teacher">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Search toggle function
        function toggleSearch() {
            const searchForm = document.getElementById('searchForm');
            const button = event.target;

            if (searchForm.style.display === 'none') {
                searchForm.style.display = 'block';
                button.textContent = 'সার্চ লুকান';
            } else {
                searchForm.style.display = 'none';
                button.textContent = 'সার্চ অপশন';
            }
        }

        // Image preview function
        function previewImage(input, previewId) {
            const preview = document.getElementById(previewId);
            preview.innerHTML = '';

            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'photo-preview';
                    preview.appendChild(img);
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const addModal = document.getElementById('addModal');
            const editModal = document.getElementById('editModal');

            if (event.target == addModal) {
                addModal.style.display = 'none';
            }
            if (event.target == editModal) {
                editModal.style.display = 'none';
            }
        }

        // Auto-submit search form on change
        document.addEventListener('DOMContentLoaded', function() {
            const searchInputs = document.querySelectorAll('#searchForm input, #searchForm select');
            searchInputs.forEach(input => {
                if (input.type !== 'submit') {
                    input.addEventListener('change', function() {
                        // Auto-submit after a short delay for text inputs
                        if (input.type === 'text') {
                            clearTimeout(input.searchTimeout);
                            input.searchTimeout = setTimeout(() => {
                                input.form.submit();
                            }, 1000);
                        } else {
                            input.form.submit();
                        }
                    });
                }
            });
        });

        // Phone number formatting
        document.addEventListener('DOMContentLoaded', function() {
            const phoneInputs = document.querySelectorAll('input[type="tel"]');
            phoneInputs.forEach(input => {
                input.addEventListener('input', function() {
                    let value = this.value.replace(/\D/g, '');
                    if (value.length > 11) {
                        value = value.substring(0, 11);
                    }
                    this.value = value;
                });
            });
        });
    </script>
</body>
</html>
