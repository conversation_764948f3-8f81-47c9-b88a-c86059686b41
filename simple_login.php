<?php
session_start();

// Simple login for demo purposes
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Simple demo credentials
    if ($username === 'admin' && $password === 'admin') {
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'Admin';
        $_SESSION['role'] = 'admin';
        header('Location: admin/dashboard.php');
        exit;
    } else {
        $error = 'ভুল ইউজারনেম বা পাসওয়ার্ড!';
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .login-header {
            margin-bottom: 2rem;
        }
        
        .login-title {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 600;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }
        
        .demo-info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border: 1px solid #bee5eb;
            font-size: 0.9rem;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            border: 1px solid #dee2e6;
        }
        
        .demo-credentials h4 {
            margin: 0 0 0.5rem 0;
            color: #495057;
        }
        
        .demo-credentials p {
            margin: 0.25rem 0;
            font-family: monospace;
            background: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }
        
        .quick-links {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }
        
        .quick-links a {
            display: inline-block;
            margin: 0.25rem 0.5rem;
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .quick-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">🏫 স্কুল ব্যবস্থাপনা</h1>
            <p class="login-subtitle">সিস্টেমে প্রবেশ করুন</p>
        </div>
        
        <div class="demo-info">
            <strong>ডেমো সিস্টেম:</strong> এটি একটি ডেমো ভার্সন। নিচের ক্রেডেনশিয়াল ব্যবহার করুন।
        </div>
        
        <?php if (isset($error)): ?>
            <div class="error-message">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="username" class="form-label">ইউজারনেম</label>
                <input type="text" id="username" name="username" class="form-input" required>
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">পাসওয়ার্ড</label>
                <input type="password" id="password" name="password" class="form-input" required>
            </div>
            
            <button type="submit" class="login-btn">
                🔐 লগইন করুন
            </button>
        </form>
        
        <div class="demo-credentials">
            <h4>ডেমো ক্রেডেনশিয়াল:</h4>
            <p><strong>ইউজারনেম:</strong> admin</p>
            <p><strong>পাসওয়ার্ড:</strong> admin</p>
        </div>
        
        <div class="quick-links">
            <a href="test_dashboard.php">🎯 ডেমো ড্যাশবোর্ড</a>
            <a href="sidebar_demo.html">📋 সাইডবার ডেমো</a>
            <a href="admin/students.php">👨‍🎓 ছাত্র পেজ</a>
        </div>
    </div>
</body>
</html>
