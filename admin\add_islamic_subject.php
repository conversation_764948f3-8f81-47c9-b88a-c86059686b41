<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Adding Islamic Education Subject</h2>";
    
    // Check if Islamic Education already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM subjects WHERE subject_name LIKE '%ইসলাম%' OR subject_code = 'ISL101'");
    $stmt->execute();
    $exists = $stmt->fetchColumn();
    
    if ($exists > 0) {
        echo "<p style='color: orange;'>ইসলাম শিক্ষা বিষয়টি ইতিমধ্যে বিদ্যমান।</p>";
    } else {
        // Add Islamic Education subject
        $stmt = $pdo->prepare("INSERT INTO subjects (subject_name, subject_code, description, status) VALUES (?, ?, ?, ?)");
        $stmt->execute([
            'ইসলাম শিক্ষা',
            'ISL101', 
            'ইসলামী শিক্ষা ও নৈতিকতা',
            'active'
        ]);
        
        echo "<p style='color: green;'>✅ ইসলাম শিক্ষা বিষয় সফলভাবে যোগ করা হয়েছে!</p>";
    }
    
    // Add more Islamic/Religious subjects
    $additional_subjects = [
        ['আরবি', 'ARB101', 'আরবি ভাষা ও সাহিত্য'],
        ['কুরআন মজিদ', 'QUR101', 'কুরআন তিলাওয়াত ও হিফজ'],
        ['হাদিস শরীফ', 'HAD101', 'হাদিস শরীফ ও সুন্নাহ'],
        ['ফিকহ', 'FIQ101', 'ইসলামী আইন ও বিধান'],
        ['আকাইদ', 'AQD101', 'ইসলামী বিশ্বাস ও আকিদা'],
        ['সীরাত', 'SIR101', 'রাসূল (সা.) এর জীবনী'],
        ['তাজবীদ', 'TAJ101', 'কুরআন তিলাওয়াতের নিয়মকানুন']
    ];
    
    echo "<h3>Adding Additional Islamic Subjects:</h3>";
    
    foreach ($additional_subjects as $subject) {
        // Check if subject already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM subjects WHERE subject_code = ?");
        $stmt->execute([$subject[1]]);
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO subjects (subject_name, subject_code, description, status) VALUES (?, ?, ?, 'active')");
            $stmt->execute($subject);
            echo "<p style='color: green;'>✅ {$subject[0]} যোগ করা হয়েছে</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ {$subject[0]} ইতিমধ্যে বিদ্যমান</p>";
        }
    }
    
    // Show all Islamic/Religious subjects
    echo "<h3>ইসলামী শিক্ষা বিষয়সমূহ:</h3>";
    
    $stmt = $pdo->query("
        SELECT * FROM subjects 
        WHERE subject_name LIKE '%ইসলাম%' 
           OR subject_name LIKE '%আরবি%' 
           OR subject_name LIKE '%কুরআন%' 
           OR subject_name LIKE '%হাদিস%' 
           OR subject_name LIKE '%ফিকহ%' 
           OR subject_name LIKE '%আকাইদ%' 
           OR subject_name LIKE '%সীরাত%' 
           OR subject_name LIKE '%তাজবীদ%'
           OR subject_name LIKE '%ধর্ম%'
        ORDER BY subject_name
    ");
    $islamic_subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($islamic_subjects)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #e8f5e8;'>";
        echo "<th style='padding: 10px;'>বিষয়ের নাম</th>";
        echo "<th style='padding: 10px;'>বিষয় কোড</th>";
        echo "<th style='padding: 10px;'>বিবরণ</th>";
        echo "<th style='padding: 10px;'>অবস্থা</th>";
        echo "</tr>";
        
        foreach ($islamic_subjects as $subject) {
            echo "<tr>";
            echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($subject['subject_name']) . "</td>";
            echo "<td style='padding: 8px; background: #e3f2fd; color: #1976d2;'>" . htmlspecialchars($subject['subject_code']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($subject['description']) . "</td>";
            echo "<td style='padding: 8px;'>";
            if ($subject['status'] == 'active') {
                echo "<span style='background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px;'>সক্রিয়</span>";
            } else {
                echo "<span style='background: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 4px;'>নিষ্ক্রিয়</span>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<br><h3>✅ ইসলামী শিক্ষা বিষয়সমূহ সফলভাবে যোগ করা হয়েছে!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px;">
    <a href="subjects.php" style="display: inline-block; padding: 15px 30px; background: #17a2b8; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        📚 বিষয় ব্যবস্থাপনায় ফিরে যান
    </a>
</div>

<script>
// Auto redirect after 3 seconds
setTimeout(function() {
    window.location.href = 'subjects.php?search_name=ইসলাম শিক্ষা&search_status=active';
}, 3000);
</script>
