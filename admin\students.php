<?php
require_once '../config/database.php';
checkRole('admin');

$db = getDB();
$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// ক্লাসের তালিকা পাওয়া
$classes = $db->select("SELECT * FROM classes WHERE status = 'active' ORDER BY class_name");

// AJAX রিকুয়েস্ট হ্যান্ডলিং
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] == 'add') {
        // নতুন ছাত্র যোগ করা
        $student_id = sanitize($_POST['student_id']);
        $first_name = sanitize($_POST['first_name']);
        $last_name = sanitize($_POST['last_name']);
        $class_id = (int)$_POST['class_id'];
        $roll_number = (int)$_POST['roll_number'];
        $date_of_birth = $_POST['date_of_birth'];
        $gender = sanitize($_POST['gender']);
        $father_name = sanitize($_POST['father_name']);
        $mother_name = sanitize($_POST['mother_name']);
        $guardian_name = sanitize($_POST['guardian_name']);
        $guardian_relation = sanitize($_POST['guardian_relation']);
        $guardian_phone = sanitize($_POST['guardian_phone']);
        $guardian_email = sanitize($_POST['guardian_email']);
        $guardian_address = sanitize($_POST['guardian_address']);
        $phone = sanitize($_POST['phone']);
        $parent_phone = sanitize($_POST['parent_phone']);
        $address = sanitize($_POST['address']);
        $username = sanitize($_POST['username']);
        $email = sanitize($_POST['email']);
        $password = $_POST['password'];

        // ছবি আপলোড হ্যান্ডলিং
        $photo_path = null;
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            $max_size = 2 * 1024 * 1024; // 2MB

            if (in_array($_FILES['photo']['type'], $allowed_types) && $_FILES['photo']['size'] <= $max_size) {
                $upload_dir = '../uploads/students/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_extension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
                $new_filename = $student_id . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $new_filename;

                if (move_uploaded_file($_FILES['photo']['tmp_name'], $upload_path)) {
                    $photo_path = 'uploads/students/' . $new_filename;
                }
            }
        }
        
        // ভ্যালিডেশন
        $errors = [];
        
        // ছাত্র ID চেক
        $existing_student = $db->selectOne("SELECT id FROM students WHERE student_id = ?", [$student_id]);
        if ($existing_student) {
            $errors['student_id'] = 'এই ছাত্র ID ইতিমধ্যে ব্যবহৃত হয়েছে';
        }
        
        // রোল নম্বর চেক
        $existing_roll = $db->selectOne("SELECT id FROM students WHERE class_id = ? AND roll_number = ?", [$class_id, $roll_number]);
        if ($existing_roll) {
            $errors['roll_number'] = 'এই ক্লাসে এই রোল নম্বর ইতিমধ্যে ব্যবহৃত হয়েছে';
        }
        
        // ইউজারনেম চেক
        $existing_user = $db->selectOne("SELECT id FROM users WHERE username = ?", [$username]);
        if ($existing_user) {
            $errors['username'] = 'এই ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে';
        }
        
        // ইমেইল চেক
        $existing_email = $db->selectOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existing_email) {
            $errors['email'] = 'এই ইমেইল ইতিমধ্যে ব্যবহৃত হয়েছে';
        }
        
        if (empty($errors)) {
            $db->beginTransaction();
            try {
                // ইউজার তৈরি করা
                $hashed_password = hashPassword($password);
                $user_id = $db->insert("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'student')", 
                    [$username, $hashed_password, $email]);
                
                if ($user_id) {
                    // ছাত্র তৈরি করা
                    $student_insert_id = $db->insert("INSERT INTO students (user_id, student_id, first_name, last_name, class_id, roll_number, date_of_birth, gender, photo, father_name, mother_name, guardian_name, guardian_relation, guardian_phone, guardian_email, guardian_address, phone, parent_phone, address, admission_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())",
                        [$user_id, $student_id, $first_name, $last_name, $class_id, $roll_number, $date_of_birth, $gender, $photo_path, $father_name, $mother_name, $guardian_name, $guardian_relation, $guardian_phone, $guardian_email, $guardian_address, $phone, $parent_phone, $address]);
                    
                    if ($student_insert_id) {
                        $db->commit();
                        echo json_encode(['success' => true, 'message' => 'ছাত্র সফলভাবে যোগ করা হয়েছে', 'reload_data' => true]);
                    } else {
                        $db->rollback();
                        echo json_encode(['success' => false, 'message' => 'ছাত্র যোগ করতে সমস্যা হয়েছে']);
                    }
                } else {
                    $db->rollback();
                    echo json_encode(['success' => false, 'message' => 'ইউজার তৈরি করতে সমস্যা হয়েছে']);
                }
            } catch (Exception $e) {
                $db->rollback();
                echo json_encode(['success' => false, 'message' => 'একটি ত্রুটি ঘটেছে']);
            }
        } else {
            echo json_encode(['success' => false, 'errors' => $errors]);
        }
    } elseif ($_POST['action'] == 'delete') {
        $student_id = (int)$_POST['student_id'];
        
        $db->beginTransaction();
        try {
            // ছাত্রের ইউজার ID পাওয়া
            $student = $db->selectOne("SELECT user_id FROM students WHERE id = ?", [$student_id]);
            
            if ($student) {
                // ছাত্র ডিলিট করা
                $db->delete("DELETE FROM students WHERE id = ?", [$student_id]);
                
                // ইউজার ডিলিট করা
                $db->delete("DELETE FROM users WHERE id = ?", [$student['user_id']]);
                
                $db->commit();
                echo json_encode(['success' => true, 'message' => 'ছাত্র সফলভাবে মুছে ফেলা হয়েছে', 'reload_data' => true]);
            } else {
                echo json_encode(['success' => false, 'message' => 'ছাত্র পাওয়া যায়নি']);
            }
        } catch (Exception $e) {
            $db->rollback();
            echo json_encode(['success' => false, 'message' => 'মুছে ফেলতে সমস্যা হয়েছে']);
        }
    }
    exit;
}

// ছাত্রদের তালিকা পাওয়া
$students = $db->select("
    SELECT s.*, c.class_name, c.section, u.username, u.email
    FROM students s
    JOIN classes c ON s.class_id = c.id
    JOIN users u ON s.user_id = u.id
    WHERE s.status = 'active'
    ORDER BY c.class_name, s.roll_number
");
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">ছাত্র ব্যবস্থাপনা</h1>
                <p class="content-subtitle">সকল ছাত্রের তথ্য দেখুন এবং নতুন ছাত্র যোগ করুন</p>
            </div>

            <div class="content-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 style="margin: 0; color: #2c3e50;">ছাত্রদের তালিকা</h2>
                    <button class="btn btn-primary" data-modal="addStudentModal">
                        <span style="margin-right: 0.5rem;">➕</span>
                        নতুন ছাত্র যোগ করুন
                    </button>
                </div>

        <!-- Students Table -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">ছাত্রদের তালিকা</h2>
            </div>
            
            <?php if (!empty($students)): ?>
                <table class="table data-table">
                    <thead>
                        <tr>
                            <th>ছবি</th>
                            <th>ছাত্র ID</th>
                            <th>নাম</th>
                            <th>পিতার নাম</th>
                            <th>মাতার নাম</th>
                            <th>ক্লাস</th>
                            <th>রোল নম্বর</th>
                            <th>ফোন</th>
                            <th>কার্যক্রম</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($student['photo'])): ?>
                                        <img src="../<?php echo htmlspecialchars($student['photo']); ?>"
                                             alt="ছাত্রের ছবি"
                                             style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                                    <?php else: ?>
                                        <div style="width: 50px; height: 50px; border-radius: 50%; background: #ddd; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                                            ছবি নেই
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                <td><?php echo htmlspecialchars($student['father_name'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($student['mother_name'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?></td>
                                <td><?php echo htmlspecialchars($student['roll_number']); ?></td>
                                <td><?php echo htmlspecialchars($student['phone']); ?></td>
                                <td>
                                    <button class="btn btn-warning btn-sm" onclick="editStudent(<?php echo $student['id']; ?>)">সম্পাদনা</button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteStudent(<?php echo $student['id']; ?>)">মুছুন</button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>কোন ছাত্র পাওয়া যায়নি।</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Student Modal -->
    <div id="addStudentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="drag-indicator"></div>
                <h2>নতুন ছাত্র যোগ করুন</h2>
                <button class="close" type="button">&times;</button>
            </div>
            <div class="modal-body">

            <form id="addStudentForm" data-ajax action="" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="ajax" value="1">
                <input type="hidden" name="action" value="add">

                <div class="form-group">
                    <label for="photo" class="form-label">ছাত্রের ছবি</label>
                    <input type="file" id="photo" name="photo" class="form-control" accept="image/*">
                    <small class="text-muted">সর্বোচ্চ ২MB, JPG/PNG/GIF ফরম্যাট</small>
                </div>

                <div class="form-group">
                    <label for="student_id" class="form-label">ছাত্র ID *</label>
                    <input type="text" id="student_id" name="student_id" class="form-control" required>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="first_name" class="form-label">প্রথম নাম *</label>
                        <input type="text" id="first_name" name="first_name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="last_name" class="form-label">শেষ নাম *</label>
                        <input type="text" id="last_name" name="last_name" class="form-control" required>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="class_id" class="form-label">ক্লাস *</label>
                        <select id="class_id" name="class_id" class="form-select" required>
                            <option value="">ক্লাস নির্বাচন করুন</option>
                            <?php foreach ($classes as $class): ?>
                                <option value="<?php echo $class['id']; ?>">
                                    <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="roll_number" class="form-label">রোল নম্বর *</label>
                        <input type="number" id="roll_number" name="roll_number" class="form-control" required>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="date_of_birth" class="form-label">জন্ম তারিখ</label>
                        <input type="date" id="date_of_birth" name="date_of_birth" class="form-control">
                    </div>

                    <div class="form-group">
                        <label for="gender" class="form-label">লিঙ্গ</label>
                        <select id="gender" name="gender" class="form-select">
                            <option value="">নির্বাচন করুন</option>
                            <option value="male">পুরুষ</option>
                            <option value="female">মহিলা</option>
                            <option value="other">অন্যান্য</option>
                        </select>
                    </div>
                </div>

                <!-- পিতা-মাতার তথ্য -->
                <div class="form-section parent-info">
                    <h3 class="form-section-header parent-info">👨‍👩‍👧‍👦 পিতা-মাতার তথ্য</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="father_name" class="form-label">পিতার নাম</label>
                            <input type="text" id="father_name" name="father_name" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="mother_name" class="form-label">মাতার নাম</label>
                            <input type="text" id="mother_name" name="mother_name" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- অভিভাবকের তথ্য -->
                <div class="form-section guardian-info">
                    <h3 class="form-section-header guardian-info">🏠 অভিভাবকের তথ্য</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="guardian_name" class="form-label">অভিভাবকের নাম</label>
                            <input type="text" id="guardian_name" name="guardian_name" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="guardian_relation" class="form-label">সম্পর্ক</label>
                            <select id="guardian_relation" name="guardian_relation" class="form-select">
                                <option value="">নির্বাচন করুন</option>
                                <option value="পিতা">পিতা</option>
                                <option value="মাতা">মাতা</option>
                                <option value="দাদা">দাদা</option>
                                <option value="দাদী">দাদী</option>
                                <option value="নানা">নানা</option>
                                <option value="নানী">নানী</option>
                                <option value="চাচা">চাচা</option>
                                <option value="চাচী">চাচী</option>
                                <option value="মামা">মামা</option>
                                <option value="মামী">মামী</option>
                                <option value="অন্যান্য">অন্যান্য</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="guardian_phone" class="form-label">অভিভাবকের ফোন</label>
                            <input type="tel" id="guardian_phone" name="guardian_phone" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="guardian_email" class="form-label">অভিভাবকের ইমেইল</label>
                            <input type="email" id="guardian_email" name="guardian_email" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="guardian_address" class="form-label">অভিভাবকের ঠিকানা</label>
                        <textarea id="guardian_address" name="guardian_address" class="form-control" rows="2"></textarea>
                    </div>
                </div>

                <!-- যোগাযোগের তথ্য -->
                <div class="form-section contact-info">
                    <h3 class="form-section-header contact-info">📞 যোগাযোগের তথ্য</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="phone" class="form-label">ছাত্রের ফোন নম্বর</label>
                            <input type="tel" id="phone" name="phone" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="parent_phone" class="form-label">পিতা-মাতার ফোন</label>
                            <input type="tel" id="parent_phone" name="parent_phone" class="form-control">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address" class="form-label">ঠিকানা</label>
                        <textarea id="address" name="address" class="form-control" rows="3"></textarea>
                    </div>
                </div>

                <!-- লগইন তথ্য -->
                <div class="form-section login-info">
                    <h3 class="form-section-header login-info">🔐 লগইন তথ্য</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="username" class="form-label">ইউজারনেম *</label>
                            <input type="text" id="username" name="username" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">ইমেইল *</label>
                            <input type="email" id="email" name="email" class="form-control" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">পাসওয়ার্ড *</label>
                        <input type="password" id="password" name="password" class="form-control" required>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('addStudentModal').style.display='none'">বাতিল</button>
                    <button type="submit" class="btn btn-primary">ছাত্র যোগ করুন</button>
                </div>
            </form>
            </div>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script>
        function deleteStudent(studentId) {
            if (SchoolManagement.confirmDelete('আপনি কি নিশ্চিত যে এই ছাত্রকে মুছে ফেলতে চান?')) {
                const formData = new FormData();
                formData.append('ajax', '1');
                formData.append('action', 'delete');
                formData.append('student_id', studentId);

                fetch('students.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        SchoolManagement.showAlert(data.message, 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        SchoolManagement.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    SchoolManagement.showAlert('একটি ত্রুটি ঘটেছে', 'danger');
                });
            }
        }

        function editStudent(studentId) {
            // Edit functionality will be implemented later
            SchoolManagement.showAlert('সম্পাদনা ফিচার শীঘ্রই আসছে', 'info');
        }

        // Handle form submission success
        document.getElementById('addStudentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // ফর্ম ডেটা তৈরি করা (ফাইল আপলোডের জন্য FormData ব্যবহার)
            const formData = new FormData(this);

            fetch('students.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    SchoolManagement.showAlert(data.message, 'success');
                    const modal = document.getElementById('addStudentModal');
                    modal.style.display = 'none';
                    // Reset modal position
                    const modalContent = modal.querySelector('.modal-content');
                    if (modalContent) {
                        modalContent.style.transform = 'translate(0px, 0px)';
                    }
                    this.reset();
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    SchoolManagement.showAlert(data.message, 'danger');
                }
            })
            .catch(error => {
                SchoolManagement.showAlert('একটি ত্রুটি ঘটেছে', 'danger');
            });
        });
    </script>
            </div>
        </div>
    </div>
</body>
</html>
