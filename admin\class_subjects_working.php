<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// COMPLETE DATABASE SETUP - Fix all issues at once
try {
    // 1. Fix classes table
    $stmt = $pdo->query("SHOW COLUMNS FROM classes LIKE 'academic_year'");
    if (!$stmt->fetch()) {
        $pdo->exec("ALTER TABLE classes ADD COLUMN academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "'");
        $pdo->exec("UPDATE classes SET academic_year = '" . date('Y') . "' WHERE academic_year = '' OR academic_year IS NULL");
    }
    
    // 2. Drop and recreate class_subjects table to ensure correct structure
    $pdo->exec("DROP TABLE IF EXISTS class_subjects");
    $pdo->exec("CREATE TABLE class_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        subject_id INT NOT NULL,
        academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_class_subject (class_id, subject_id, academic_year)
    )");
    
    // 3. Ensure we have sample classes
    $stmt = $pdo->query("SELECT COUNT(*) FROM classes");
    if ($stmt->fetchColumn() == 0) {
        $sample_classes = [
            ['প্রথম শ্রেণী', 'ক'], ['দ্বিতীয় শ্রেণী', 'ক'], ['তৃতীয় শ্রেণী', 'ক'],
            ['চতুর্থ শ্রেণী', 'ক'], ['পঞ্চম শ্রেণী', 'ক'], ['ষষ্ঠ শ্রেণী', 'ক'],
            ['সপ্তম শ্রেণী', 'ক'], ['অষ্টম শ্রেণী', 'ক'], ['নবম শ্রেণী', 'ক'], ['দশম শ্রেণী', 'ক']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year, status) VALUES (?, ?, ?, 'active')");
        foreach ($sample_classes as $class) {
            $stmt->execute([$class[0], $class[1], date('Y')]);
        }
    }
    
} catch (PDOException $e) {
    $error = "Database setup error: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'assign_subjects') {
        try {
            $class_id = (int)$_POST['class_id'];
            $subject_ids = $_POST['subject_ids'] ?? [];
            $academic_year = trim($_POST['academic_year']) ?: date('Y');
            
            if (empty($class_id)) {
                throw new Exception('ক্লাস নির্বাচন আবশ্যক।');
            }
            
            // Remove existing assignments
            $stmt = $pdo->prepare("DELETE FROM class_subjects WHERE class_id = ? AND academic_year = ?");
            $stmt->execute([$class_id, $academic_year]);
            
            // Add new assignments
            if (!empty($subject_ids)) {
                $stmt = $pdo->prepare("INSERT INTO class_subjects (class_id, subject_id, academic_year) VALUES (?, ?, ?)");
                foreach ($subject_ids as $subject_id) {
                    $stmt->execute([$class_id, (int)$subject_id, $academic_year]);
                }
                $message = count($subject_ids) . 'টি বিষয় সফলভাবে নির্ধারণ করা হয়েছে!';
            } else {
                $message = 'ক্লাসের সকল বিষয় সরানো হয়েছে।';
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'remove_subject') {
        try {
            $class_subject_id = (int)$_POST['class_subject_id'];
            $stmt = $pdo->prepare("DELETE FROM class_subjects WHERE id = ?");
            $stmt->execute([$class_subject_id]);
            $message = 'বিষয় সফলভাবে সরানো হয়েছে!';
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get filter parameters
$selected_class = $_GET['class_id'] ?? '';
$selected_year = $_GET['academic_year'] ?? date('Y');

// Get data - SAFE QUERIES
$classes = [];
$subjects = [];
$academic_years = [date('Y'), (date('Y')-1), (date('Y')+1)];
$class_subjects = [];
$overview = [];

try {
    // Get classes
    $stmt = $pdo->query("SELECT id, class_name, section, academic_year FROM classes WHERE status = 'active' ORDER BY class_name, section");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get subjects
    $stmt = $pdo->query("SELECT id, subject_name, subject_code FROM subjects WHERE status = 'active' ORDER BY subject_name");
    $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get academic years from classes
    $stmt = $pdo->query("SELECT DISTINCT academic_year FROM classes ORDER BY academic_year DESC");
    $years_from_db = $stmt->fetchAll(PDO::FETCH_COLUMN);
    if (!empty($years_from_db)) {
        $academic_years = $years_from_db;
    }
    
    // Get class-subject assignments for selected class
    if (!empty($selected_class)) {
        $stmt = $pdo->prepare("
            SELECT cs.*, s.subject_name, s.subject_code
            FROM class_subjects cs
            JOIN subjects s ON cs.subject_id = s.id
            WHERE cs.class_id = ? AND cs.academic_year = ?
            ORDER BY s.subject_name
        ");
        $stmt->execute([$selected_class, $selected_year]);
        $class_subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get overview - SIMPLIFIED SAFE QUERY
    $stmt = $pdo->prepare("
        SELECT c.id as class_id, c.class_name, c.section, c.academic_year,
               (SELECT COUNT(*) FROM class_subjects cs WHERE cs.class_id = c.id AND cs.academic_year = ?) as subject_count,
               (SELECT GROUP_CONCAT(s.subject_name ORDER BY s.subject_name SEPARATOR ', ') 
                FROM class_subjects cs 
                JOIN subjects s ON cs.subject_id = s.id 
                WHERE cs.class_id = c.id AND cs.academic_year = ?) as subjects
        FROM classes c
        WHERE c.status = 'active'
        ORDER BY c.class_name, c.section
    ");
    $stmt->execute([$selected_year, $selected_year]);
    $overview = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Data loading error: " . $e->getMessage();
}

// Calculate statistics
$total_classes = count($classes);
$total_subjects = count($subjects);
$total_connections = array_sum(array_column($overview, 'subject_count'));
$active_classes = count(array_filter($overview, fn($o) => $o['subject_count'] > 0));
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ক্লাস-বিষয় সংযোগ - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .class-subjects-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid #e9ecef;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .assignment-section {
            background: #e8f5e8;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .subject-checkbox {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .subject-checkbox:hover {
            border-color: #007bff;
            transform: translateY(-2px);
        }
        
        .subject-checkbox.selected {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .subject-checkbox input[type="checkbox"] {
            margin-right: 0.5rem;
            transform: scale(1.2);
        }
        
        .subject-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }
        
        .subject-code {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .assigned-subjects {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .assigned-subject-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
        }
        
        .assigned-subject-info {
            flex: 1;
        }
        
        .assigned-subject-name {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .assigned-subject-code {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .overview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .overview-table th,
        .overview-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .overview-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .overview-table tr:hover {
            background: #f8f9fa;
        }
        
        .subject-count-badge {
            background: #007bff;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .subjects-list {
            max-width: 400px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .assign-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }
        
        .assign-btn:hover {
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .subjects-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .overview-table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">🔗 ক্লাস-বিষয় সংযোগ</h1>
                <p class="content-subtitle">প্রতিটি ক্লাসের জন্য বিষয় নির্ধারণ ও ব্যবস্থাপনা</p>
            </div>

            <div class="content-body">
                <div class="class-subjects-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $total_classes; ?></div>
                            <div class="stat-label">মোট ক্লাস</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $total_subjects; ?></div>
                            <div class="stat-label">মোট বিষয়</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $total_connections; ?></div>
                            <div class="stat-label">মোট সংযোগ</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $active_classes; ?></div>
                            <div class="stat-label">সক্রিয় ক্লাস</div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">🔍 ক্লাস ও শিক্ষাবর্ষ নির্বাচন</h3>

                        <form method="GET" class="filter-grid">
                            <div class="form-group">
                                <label class="form-label">ক্লাস নির্বাচন করুন</label>
                                <select name="class_id" class="form-control" onchange="this.form.submit()">
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php foreach ($classes as $class): ?>
                                        <option value="<?php echo $class['id']; ?>"
                                                <?php echo ($selected_class == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section'] . ' (' . $class['academic_year'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">শিক্ষাবর্ষ</label>
                                <select name="academic_year" class="form-control" onchange="this.form.submit()">
                                    <?php foreach ($academic_years as $year): ?>
                                        <option value="<?php echo htmlspecialchars($year); ?>"
                                                <?php echo ($selected_year == $year) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($year); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>

                    <?php if (!empty($selected_class)): ?>
                        <!-- Subject Assignment Section -->
                        <div class="assignment-section">
                            <h3 style="color: #155724; margin-bottom: 1rem;">
                                📚 বিষয় নির্ধারণ -
                                <?php
                                $selected_class_info = '';
                                foreach ($classes as $class) {
                                    if ($class['id'] == $selected_class) {
                                        $selected_class_info = $class['class_name'] . ' - ' . $class['section'];
                                        break;
                                    }
                                }
                                echo htmlspecialchars($selected_class_info);
                                ?>
                                (<?php echo htmlspecialchars($selected_year); ?>)
                            </h3>

                            <form method="POST" id="assignForm">
                                <input type="hidden" name="action" value="assign_subjects">
                                <input type="hidden" name="class_id" value="<?php echo htmlspecialchars($selected_class); ?>">
                                <input type="hidden" name="academic_year" value="<?php echo htmlspecialchars($selected_year); ?>">

                                <div class="subjects-grid">
                                    <?php
                                    $assigned_subject_ids = array_column($class_subjects, 'subject_id');
                                    foreach ($subjects as $subject):
                                        $is_assigned = in_array($subject['id'], $assigned_subject_ids);
                                    ?>
                                        <label class="subject-checkbox <?php echo $is_assigned ? 'selected' : ''; ?>">
                                            <input type="checkbox" name="subject_ids[]" value="<?php echo $subject['id']; ?>"
                                                   <?php echo $is_assigned ? 'checked' : ''; ?>
                                                   onchange="toggleSubjectSelection(this)">
                                            <div class="subject-name"><?php echo htmlspecialchars($subject['subject_name']); ?></div>
                                            <div class="subject-code"><?php echo htmlspecialchars($subject['subject_code']); ?></div>
                                        </label>
                                    <?php endforeach; ?>
                                </div>

                                <button type="submit" class="assign-btn">
                                    💾 বিষয় নির্ধারণ সংরক্ষণ করুন
                                </button>
                            </form>
                        </div>

                        <!-- Currently Assigned Subjects -->
                        <?php if (!empty($class_subjects)): ?>
                            <div class="assigned-subjects">
                                <h3 style="color: #2c3e50; margin-bottom: 1rem;">
                                    ✅ বর্তমানে নির্ধারিত বিষয়সমূহ (<?php echo count($class_subjects); ?>টি)
                                </h3>

                                <?php foreach ($class_subjects as $cs): ?>
                                    <div class="assigned-subject-item">
                                        <div class="assigned-subject-info">
                                            <div class="assigned-subject-name"><?php echo htmlspecialchars($cs['subject_name']); ?></div>
                                            <div class="assigned-subject-code"><?php echo htmlspecialchars($cs['subject_code']); ?></div>
                                        </div>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="remove_subject">
                                            <input type="hidden" name="class_subject_id" value="<?php echo $cs['id']; ?>">
                                            <button type="submit" class="remove-btn"
                                                    onclick="return confirm('আপনি কি নিশ্চিত যে এই বিষয়টি সরাতে চান?')">
                                                🗑️ সরান
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Overview Section -->
                    <div class="assigned-subjects">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">
                            📊 সকল ক্লাসের বিষয় বিন্যাস (<?php echo htmlspecialchars($selected_year); ?>)
                        </h3>

                        <div style="overflow-x: auto;">
                            <table class="overview-table">
                                <thead>
                                    <tr>
                                        <th>ক্লাস</th>
                                        <th>সেকশন</th>
                                        <th>শিক্ষাবর্ষ</th>
                                        <th>বিষয় সংখ্যা</th>
                                        <th>নির্ধারিত বিষয়সমূহ</th>
                                        <th>কার্যক্রম</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($overview)): ?>
                                        <tr>
                                            <td colspan="6" style="text-align: center; padding: 3rem; color: #6c757d;">
                                                <h4>📚 কোন ক্লাস পাওয়া যায়নি</h4>
                                                <p>প্রথমে ক্লাস তৈরি করুন।</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($overview as $class): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($class['class_name']); ?></strong></td>
                                                <td><?php echo htmlspecialchars($class['section']); ?></td>
                                                <td><?php echo htmlspecialchars($class['academic_year']); ?></td>
                                                <td>
                                                    <span class="subject-count-badge">
                                                        <?php echo $class['subject_count']; ?> টি
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="subjects-list" title="<?php echo htmlspecialchars($class['subjects'] ?? 'কোন বিষয় নির্ধারিত নয়'); ?>">
                                                        <?php echo htmlspecialchars($class['subjects'] ?? 'কোন বিষয় নির্ধারিত নয়'); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <a href="?class_id=<?php echo $class['class_id']; ?>&academic_year=<?php echo urlencode($selected_year); ?>"
                                                       style="background: #007bff; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none; font-size: 0.9rem;">
                                                        ✏️ সম্পাদনা
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSubjectSelection(checkbox) {
            const label = checkbox.closest('.subject-checkbox');
            if (checkbox.checked) {
                label.classList.add('selected');
            } else {
                label.classList.remove('selected');
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const form = document.getElementById('assignForm');
                if (form) {
                    form.submit();
                }
            }
        });
    </script>
</body>
</html>
