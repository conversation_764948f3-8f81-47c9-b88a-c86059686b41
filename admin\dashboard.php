<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();

// Get dashboard statistics
$total_students = $db->selectOne("SELECT COUNT(*) as count FROM students WHERE status = 'active'")['count'];
$total_teachers = $db->selectOne("SELECT COUNT(*) as count FROM teachers WHERE status = 'active'")['count'] ?? 0;
$total_classes = $db->selectOne("SELECT COUNT(*) as count FROM classes WHERE status = 'active'")['count'];
$total_subjects = $db->selectOne("SELECT COUNT(*) as count FROM subjects WHERE status = 'active'")['count'] ?? 0;

// Recent students
$recent_students = $db->select("
    SELECT s.*, c.class_name, c.section
    FROM students s
    JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY s.created_at DESC
    LIMIT 5
");

// Monthly admissions
$monthly_admissions = $db->select("
    SELECT
        MONTH(admission_date) as month,
        YEAR(admission_date) as year,
        COUNT(*) as count
    FROM students
    WHERE status = 'active'
    AND admission_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY YEAR(admission_date), MONTH(admission_date)
    ORDER BY year DESC, month DESC
    LIMIT 6
");
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্যাশবোর্ড - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.students { border-left-color: #3498db; }
        .stat-card.teachers { border-left-color: #2ecc71; }
        .stat-card.classes { border-left-color: #f39c12; }
        .stat-card.subjects { border-left-color: #e74c3c; }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .students .stat-icon { color: #3498db; }
        .teachers .stat-icon { color: #2ecc71; }
        .classes .stat-icon { color: #f39c12; }
        .subjects .stat-icon { color: #e74c3c; }

        .students .stat-number { color: #3498db; }
        .teachers .stat-number { color: #2ecc71; }
        .classes .stat-number { color: #f39c12; }
        .subjects .stat-number { color: #e74c3c; }

        .recent-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }

        .student-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .student-item:last-child {
            border-bottom: none;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }

        .student-info {
            flex: 1;
        }

        .student-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .student-class {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-title {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">ড্যাশবোর্ড</h1>
                <p class="content-subtitle">স্কুল ব্যবস্থাপনা সিস্টেমের সার্বিক অবস্থা</p>
            </div>

            <div class="content-body">
                <!-- Welcome Card -->
                <div class="welcome-card">
                    <h2 class="welcome-title">স্বাগতম, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h2>
                    <p class="welcome-subtitle">আজ <?php echo date('d F Y, l'); ?> - আপনার স্কুল ব্যবস্থাপনা ড্যাশবোর্ডে স্বাগতম</p>
                </div>

                <!-- Statistics Cards -->
                <div class="dashboard-grid">
                    <div class="stat-card students">
                        <div class="stat-icon">👨‍🎓</div>
                        <div class="stat-number"><?php echo number_format($total_students); ?></div>
                        <div class="stat-label">মোট ছাত্র</div>
                    </div>

                    <div class="stat-card teachers">
                        <div class="stat-icon">👩‍🏫</div>
                        <div class="stat-number"><?php echo number_format($total_teachers); ?></div>
                        <div class="stat-label">মোট শিক্ষক</div>
                    </div>

                    <div class="stat-card classes">
                        <div class="stat-icon">🏫</div>
                        <div class="stat-number"><?php echo number_format($total_classes); ?></div>
                        <div class="stat-label">মোট ক্লাস</div>
                    </div>

                    <div class="stat-card subjects">
                        <div class="stat-icon">📚</div>
                        <div class="stat-number"><?php echo number_format($total_subjects); ?></div>
                        <div class="stat-label">মোট বিষয়</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="recent-section">
                    <h3 class="section-title">⚡ দ্রুত কাজ</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <a href="students.php" class="btn btn-primary" style="text-decoration: none; text-align: center; padding: 1rem;">
                            👨‍🎓 ছাত্র ব্যবস্থাপনা
                        </a>
                        <a href="teachers.php" class="btn btn-success" style="text-decoration: none; text-align: center; padding: 1rem;">
                            👩‍🏫 শিক্ষক ব্যবস্থাপনা
                        </a>
                        <a href="classes.php" class="btn btn-warning" style="text-decoration: none; text-align: center; padding: 1rem;">
                            🏫 ক্লাস ব্যবস্থাপনা
                        </a>
                        <a href="attendance.php" class="btn btn-info" style="text-decoration: none; text-align: center; padding: 1rem;">
                            📅 উপস্থিতি নিন
                        </a>
                    </div>
                </div>

                <!-- Recent Students -->
                <div class="recent-section">
                    <h3 class="section-title">🆕 সাম্প্রতিক ভর্তি হওয়া ছাত্রগণ</h3>
                    <?php if (empty($recent_students)): ?>
                        <p style="text-align: center; color: #6c757d; padding: 2rem;">এখনো কোন ছাত্র যোগ করা হয়নি</p>
                    <?php else: ?>
                        <?php foreach ($recent_students as $student): ?>
                            <div class="student-item">
                                <div class="student-avatar">
                                    <?php echo strtoupper(substr($student['first_name'], 0, 1)); ?>
                                </div>
                                <div class="student-info">
                                    <div class="student-name">
                                        <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                    </div>
                                    <div class="student-class">
                                        <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?> |
                                        রোল: <?php echo htmlspecialchars($student['roll_number']); ?>
                                    </div>
                                </div>
                                <div style="color: #28a745; font-size: 0.8rem;">
                                    <?php echo date('d M Y', strtotime($student['admission_date'])); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

            </div>
        </div>
    </div>

    <script src="../assets/js/main.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();

            // Add some interactive effects
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Add click animation
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-5px)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
