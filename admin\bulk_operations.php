<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'bulk_delete' && isset($_POST['student_ids'])) {
        $student_ids = $_POST['student_ids'];
        $deleted_count = 0;
        
        try {
            $pdo->beginTransaction();
            
            foreach ($student_ids as $student_id) {
                // Get user_id first
                $stmt = $pdo->prepare("SELECT user_id FROM students WHERE id = ?");
                $stmt->execute([$student_id]);
                $student = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($student) {
                    // Delete student record
                    $stmt = $pdo->prepare("DELETE FROM students WHERE id = ?");
                    $stmt->execute([$student_id]);
                    
                    // Delete user record
                    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->execute([$student['user_id']]);
                    
                    $deleted_count++;
                }
            }
            
            $pdo->commit();
            $message = "$deleted_count জন ছাত্র সফলভাবে মুছে ফেলা হয়েছে।";
            
        } catch (Exception $e) {
            $pdo->rollback();
            $error = "ছাত্র মুছতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
    
    if ($action === 'bulk_update_class' && isset($_POST['student_ids']) && isset($_POST['new_class_id'])) {
        $student_ids = $_POST['student_ids'];
        $new_class_id = $_POST['new_class_id'];
        $updated_count = 0;
        
        try {
            $stmt = $pdo->prepare("UPDATE students SET class_id = ? WHERE id = ?");
            
            foreach ($student_ids as $student_id) {
                $stmt->execute([$new_class_id, $student_id]);
                $updated_count++;
            }
            
            $message = "$updated_count জন ছাত্রের ক্লাস সফলভাবে আপডেট করা হয়েছে।";
            
        } catch (Exception $e) {
            $error = "ক্লাস আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get search parameters
$search_name = $_GET['search_name'] ?? '';
$search_student_id = $_GET['search_student_id'] ?? '';
$search_class = $_GET['search_class'] ?? '';
$search_gender = $_GET['search_gender'] ?? '';
$search_admission_date_from = $_GET['search_admission_date_from'] ?? '';
$search_admission_date_to = $_GET['search_admission_date_to'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'first_name';
$sort_order = $_GET['sort_order'] ?? 'ASC';

// Build WHERE clause based on search parameters
$where_conditions = [];
$params = [];

if (!empty($search_name)) {
    $where_conditions[] = "(s.first_name LIKE ? OR s.last_name LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
    $search_term = "%$search_name%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($search_student_id)) {
    $where_conditions[] = "s.student_id LIKE ?";
    $params[] = "%$search_student_id%";
}

if (!empty($search_class)) {
    $where_conditions[] = "s.class_id = ?";
    $params[] = $search_class;
}

if (!empty($search_gender)) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $search_gender;
}

if (!empty($search_admission_date_from)) {
    $where_conditions[] = "s.admission_date >= ?";
    $params[] = $search_admission_date_from;
}

if (!empty($search_admission_date_to)) {
    $where_conditions[] = "s.admission_date <= ?";
    $params[] = $search_admission_date_to;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// Validate sort parameters
$allowed_sort_columns = ['first_name', 'last_name', 'student_id', 'class_name', 'roll_number', 'admission_date'];
if (!in_array($sort_by, $allowed_sort_columns)) {
    $sort_by = 'first_name';
}
$sort_order = strtoupper($sort_order) === 'DESC' ? 'DESC' : 'ASC';

// Get filtered students with class info
try {
    $sql = "
        SELECT s.*, c.class_name, c.section, u.username, u.email
        FROM students s
        LEFT JOIN classes c ON s.class_id = c.id
        LEFT JOIN users u ON s.user_id = u.id
        $where_clause
        ORDER BY $sort_by $sort_order
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get total count for statistics
    $count_sql = "
        SELECT COUNT(*) as total
        FROM students s
        LEFT JOIN classes c ON s.class_id = c.id
        LEFT JOIN users u ON s.user_id = u.id
        $where_clause
    ";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($params);
    $total_filtered = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

} catch (PDOException $e) {
    $students = [];
    $total_filtered = 0;
}

// Get classes for dropdown
try {
    $stmt = $pdo->query("SELECT * FROM classes ORDER BY class_name");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $classes = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বাল্ক অপারেশন - ছাত্র ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .bulk-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .search-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
        }

        .search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .search-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-toggle {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .search-group {
            display: flex;
            flex-direction: column;
        }

        .search-label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #495057;
        }

        .search-input {
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 0.95rem;
        }

        .search-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .search-btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-search {
            background: #28a745;
            color: white;
        }

        .btn-reset {
            background: #6c757d;
            color: white;
        }

        .search-stats {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .bulk-actions {
            background: #fff3cd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #ffeaa7;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .action-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .students-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .students-table th {
            background: #667eea;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .students-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .students-table tr:hover {
            background: #f8f9fa;
        }
        
        .select-all-container {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .selected-count {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 0.5rem;
        }
        
        .class-update-form {
            display: none;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .photo-preview {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">⚡ বাল্ক অপারেশন</h1>
                <p class="content-subtitle">একসাথে অনেক ছাত্রের তথ্য পরিবর্তন করুন</p>
            </div>
            
            <div class="content-body">
                <div class="bulk-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Advanced Search -->
                    <div class="search-container">
                        <div class="search-header">
                            <h3 class="search-title">
                                🔍 উন্নত সার্চ
                            </h3>
                            <button type="button" class="search-toggle" onclick="toggleSearch()">
                                সার্চ অপশন
                            </button>
                        </div>

                        <div id="searchForm" style="display: <?php echo (!empty($search_name) || !empty($search_student_id) || !empty($search_class) || !empty($search_gender) || !empty($search_admission_date_from) || !empty($search_admission_date_to)) ? 'block' : 'none'; ?>;">
                            <form method="GET" class="search-form">
                                <div class="search-group">
                                    <label class="search-label">নাম দিয়ে সার্চ</label>
                                    <input type="text" name="search_name" class="search-input"
                                           value="<?php echo htmlspecialchars($search_name); ?>"
                                           placeholder="নাম বা পদবি লিখুন">
                                </div>

                                <div class="search-group">
                                    <label class="search-label">ছাত্র ID</label>
                                    <input type="text" name="search_student_id" class="search-input"
                                           value="<?php echo htmlspecialchars($search_student_id); ?>"
                                           placeholder="STU2024001">
                                </div>

                                <div class="search-group">
                                    <label class="search-label">ক্লাস</label>
                                    <select name="search_class" class="search-input">
                                        <option value="">সব ক্লাস</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>"
                                                    <?php echo ($search_class == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="search-group">
                                    <label class="search-label">লিঙ্গ</label>
                                    <select name="search_gender" class="search-input">
                                        <option value="">সব লিঙ্গ</option>
                                        <option value="male" <?php echo ($search_gender == 'male') ? 'selected' : ''; ?>>পুরুষ</option>
                                        <option value="female" <?php echo ($search_gender == 'female') ? 'selected' : ''; ?>>মহিলা</option>
                                    </select>
                                </div>

                                <div class="search-group">
                                    <label class="search-label">ভর্তির তারিখ (শুরু)</label>
                                    <input type="date" name="search_admission_date_from" class="search-input"
                                           value="<?php echo htmlspecialchars($search_admission_date_from); ?>">
                                </div>

                                <div class="search-group">
                                    <label class="search-label">ভর্তির তারিখ (শেষ)</label>
                                    <input type="date" name="search_admission_date_to" class="search-input"
                                           value="<?php echo htmlspecialchars($search_admission_date_to); ?>">
                                </div>

                                <div class="search-group">
                                    <label class="search-label">সাজানোর ভিত্তি</label>
                                    <select name="sort_by" class="search-input">
                                        <option value="first_name" <?php echo ($sort_by == 'first_name') ? 'selected' : ''; ?>>নাম</option>
                                        <option value="last_name" <?php echo ($sort_by == 'last_name') ? 'selected' : ''; ?>>পদবি</option>
                                        <option value="student_id" <?php echo ($sort_by == 'student_id') ? 'selected' : ''; ?>>ছাত্র ID</option>
                                        <option value="class_name" <?php echo ($sort_by == 'class_name') ? 'selected' : ''; ?>>ক্লাস</option>
                                        <option value="roll_number" <?php echo ($sort_by == 'roll_number') ? 'selected' : ''; ?>>রোল নম্বর</option>
                                        <option value="admission_date" <?php echo ($sort_by == 'admission_date') ? 'selected' : ''; ?>>ভর্তির তারিখ</option>
                                    </select>
                                </div>

                                <div class="search-group">
                                    <label class="search-label">সাজানোর ক্রম</label>
                                    <select name="sort_order" class="search-input">
                                        <option value="ASC" <?php echo ($sort_order == 'ASC') ? 'selected' : ''; ?>>ছোট থেকে বড়</option>
                                        <option value="DESC" <?php echo ($sort_order == 'DESC') ? 'selected' : ''; ?>>বড় থেকে ছোট</option>
                                    </select>
                                </div>
                            </form>

                            <div class="search-actions">
                                <button type="button" class="search-btn btn-search" onclick="submitSearch()">
                                    🔍 সার্চ করুন
                                </button>
                                <button type="button" class="search-btn btn-reset" onclick="resetSearch()">
                                    🔄 রিসেট করুন
                                </button>
                            </div>
                        </div>

                        <!-- Quick Filters -->
                        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #dee2e6;">
                            <h4 style="margin-bottom: 1rem; color: #495057;">🚀 কুইক ফিল্টার:</h4>
                            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <?php foreach ($classes as $class): ?>
                                    <a href="?search_class=<?php echo $class['id']; ?>"
                                       class="btn btn-sm <?php echo ($search_class == $class['id']) ? 'btn-primary' : 'btn-outline-secondary'; ?>"
                                       style="padding: 0.25rem 0.75rem; border-radius: 15px; text-decoration: none; font-size: 0.85rem;">
                                        <?php echo htmlspecialchars($class['class_name']); ?>
                                    </a>
                                <?php endforeach; ?>

                                <a href="?search_gender=male"
                                   class="btn btn-sm <?php echo ($search_gender == 'male') ? 'btn-info' : 'btn-outline-info'; ?>"
                                   style="padding: 0.25rem 0.75rem; border-radius: 15px; text-decoration: none; font-size: 0.85rem;">
                                    👦 ছেলে
                                </a>

                                <a href="?search_gender=female"
                                   class="btn btn-sm <?php echo ($search_gender == 'female') ? 'btn-pink' : 'btn-outline-pink'; ?>"
                                   style="padding: 0.25rem 0.75rem; border-radius: 15px; text-decoration: none; font-size: 0.85rem; background: <?php echo ($search_gender == 'female') ? '#e91e63' : 'transparent'; ?>; color: <?php echo ($search_gender == 'female') ? 'white' : '#e91e63'; ?>; border: 1px solid #e91e63;">
                                    👧 মেয়ে
                                </a>

                                <a href="?search_admission_date_from=<?php echo date('Y-m-01'); ?>"
                                   class="btn btn-sm btn-outline-success"
                                   style="padding: 0.25rem 0.75rem; border-radius: 15px; text-decoration: none; font-size: 0.85rem;">
                                    📅 এই মাসে ভর্তি
                                </a>

                                <?php if (!empty($_GET)): ?>
                                    <a href="?"
                                       class="btn btn-sm btn-outline-danger"
                                       style="padding: 0.25rem 0.75rem; border-radius: 15px; text-decoration: none; font-size: 0.85rem;">
                                        🔄 সব ফিল্টার মুছুন
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Search Statistics -->
                        <?php if (isset($total_filtered)): ?>
                            <div class="search-stats">
                                📊 মোট <?php echo $total_filtered; ?> জন ছাত্র পাওয়া গেছে
                                <?php if (!empty($search_name) || !empty($search_student_id) || !empty($search_class) || !empty($search_gender) || !empty($search_admission_date_from) || !empty($search_admission_date_to)): ?>
                                    (ফিল্টার করা)
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Select All -->
                    <div class="select-all-container">
                        <label>
                            <input type="checkbox" id="selectAll"> 
                            <strong>সব ছাত্র নির্বাচন করুন</strong>
                        </label>
                        <span class="selected-count" id="selectedCount">0 টি নির্বাচিত</span>
                    </div>
                    
                    <!-- Bulk Actions -->
                    <div class="bulk-actions">
                        <h3>🔧 বাল্ক অ্যাকশন</h3>
                        <p>নির্বাচিত ছাত্রদের জন্য কাজ করুন:</p>
                        
                        <div class="action-buttons">
                            <button type="button" class="action-btn btn-danger" onclick="confirmBulkDelete()">
                                🗑️ নির্বাচিত ছাত্র মুছুন
                            </button>
                            <button type="button" class="action-btn btn-warning" onclick="showClassUpdateForm()">
                                📚 ক্লাস পরিবর্তন করুন
                            </button>
                            <button type="button" class="action-btn btn-info" onclick="exportSelected()">
                                📊 নির্বাচিত তথ্য এক্সপোর্ট
                            </button>
                        </div>
                        
                        <!-- Class Update Form -->
                        <div class="class-update-form" id="classUpdateForm">
                            <h4>📚 ক্লাস পরিবর্তন করুন</h4>
                            <form method="POST" style="display: flex; gap: 1rem; align-items: end;">
                                <input type="hidden" name="action" value="bulk_update_class">
                                <input type="hidden" name="student_ids[]" id="classUpdateStudentIds">
                                
                                <div style="flex: 1;">
                                    <label class="form-label">নতুন ক্লাস নির্বাচন করুন:</label>
                                    <select name="new_class_id" class="form-control" required>
                                        <option value="">ক্লাস নির্বাচন করুন</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>">
                                                <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <button type="submit" class="action-btn btn-warning">
                                    ✅ আপডেট করুন
                                </button>
                                <button type="button" class="action-btn" style="background: #6c757d; color: white;" onclick="hideClassUpdateForm()">
                                    ❌ বাতিল
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Students Table -->
                    <div style="overflow-x: auto;">
                        <table class="students-table">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAllTable">
                                    </th>
                                    <th>ছবি</th>
                                    <th>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'student_id', 'sort_order' => ($sort_by == 'student_id' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                           style="color: white; text-decoration: none;">
                                            ছাত্র ID
                                            <?php if ($sort_by == 'student_id'): ?>
                                                <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                            <?php endif; ?>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'first_name', 'sort_order' => ($sort_by == 'first_name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                           style="color: white; text-decoration: none;">
                                            নাম
                                            <?php if ($sort_by == 'first_name'): ?>
                                                <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                            <?php endif; ?>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'class_name', 'sort_order' => ($sort_by == 'class_name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                           style="color: white; text-decoration: none;">
                                            ক্লাস
                                            <?php if ($sort_by == 'class_name'): ?>
                                                <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                            <?php endif; ?>
                                        </a>
                                    </th>
                                    <th>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['sort_by' => 'roll_number', 'sort_order' => ($sort_by == 'roll_number' && $sort_order == 'ASC') ? 'DESC' : 'ASC'])); ?>"
                                           style="color: white; text-decoration: none;">
                                            রোল
                                            <?php if ($sort_by == 'roll_number'): ?>
                                                <?php echo $sort_order == 'ASC' ? '↑' : '↓'; ?>
                                            <?php endif; ?>
                                        </a>
                                    </th>
                                    <th>পিতার নাম</th>
                                    <th>ফোন</th>
                                    <th>ইমেইল</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="student-checkbox" value="<?php echo $student['id']; ?>">
                                        </td>
                                        <td>
                                            <?php if ($student['photo']): ?>
                                                <img src="../<?php echo htmlspecialchars($student['photo']); ?>" 
                                                     alt="ছবি" class="photo-preview">
                                            <?php else: ?>
                                                <div style="width: 40px; height: 40px; background: #dee2e6; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                    👤
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                        </td>
                                        <td>
                                            <span style="background: #e3f2fd; padding: 0.25rem 0.5rem; border-radius: 4px;">
                                                <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($student['roll_number']); ?></td>
                                        <td><?php echo htmlspecialchars($student['father_name']); ?></td>
                                        <td><?php echo htmlspecialchars($student['guardian_phone']); ?></td>
                                        <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if (empty($students)): ?>
                        <div style="text-align: center; padding: 3rem; color: #6c757d;">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">📚</div>
                            <h3>কোন ছাত্র পাওয়া যায়নি</h3>
                            <p>প্রথমে কিছু ছাত্র যোগ করুন।</p>
                            <a href="csv_upload.php" class="btn btn-primary">CSV আপলোড করুন</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Hidden form for bulk delete -->
    <form id="bulkDeleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="bulk_delete">
        <div id="bulkDeleteStudentIds"></div>
    </form>
    
    <script src="../assets/js/main.js"></script>
    <script>
        let selectedStudents = [];
        
        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });
        
        document.getElementById('selectAllTable').addEventListener('change', function() {
            document.getElementById('selectAll').checked = this.checked;
            document.getElementById('selectAll').dispatchEvent(new Event('change'));
        });
        
        // Individual checkbox change
        document.querySelectorAll('.student-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });
        
        function updateSelectedCount() {
            const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
            selectedStudents = Array.from(checkedBoxes).map(cb => cb.value);
            
            document.getElementById('selectedCount').textContent = selectedStudents.length + ' টি নির্বাচিত';
            
            // Update select all checkbox
            const allCheckboxes = document.querySelectorAll('.student-checkbox');
            const selectAllCheckbox = document.getElementById('selectAll');
            const selectAllTableCheckbox = document.getElementById('selectAllTable');
            
            if (selectedStudents.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
                selectAllTableCheckbox.indeterminate = false;
                selectAllTableCheckbox.checked = false;
            } else if (selectedStudents.length === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
                selectAllTableCheckbox.indeterminate = false;
                selectAllTableCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllTableCheckbox.indeterminate = true;
            }
        }
        
        function confirmBulkDelete() {
            if (selectedStudents.length === 0) {
                alert('কোন ছাত্র নির্বাচন করা হয়নি।');
                return;
            }
            
            if (confirm(`আপনি কি নিশ্চিত যে ${selectedStudents.length} জন ছাত্রকে মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।`)) {
                const form = document.getElementById('bulkDeleteForm');
                const container = document.getElementById('bulkDeleteStudentIds');
                container.innerHTML = '';
                
                selectedStudents.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'student_ids[]';
                    input.value = id;
                    container.appendChild(input);
                });
                
                form.submit();
            }
        }
        
        function showClassUpdateForm() {
            if (selectedStudents.length === 0) {
                alert('কোন ছাত্র নির্বাচন করা হয়নি।');
                return;
            }
            
            document.getElementById('classUpdateForm').style.display = 'block';
            document.getElementById('classUpdateStudentIds').value = selectedStudents.join(',');
        }
        
        function hideClassUpdateForm() {
            document.getElementById('classUpdateForm').style.display = 'none';
        }
        
        function exportSelected() {
            if (selectedStudents.length === 0) {
                alert('কোন ছাত্র নির্বাচন করা হয়নি।');
                return;
            }
            
            // Create export URL with selected student IDs
            const exportUrl = 'export_students.php?ids=' + selectedStudents.join(',');
            window.open(exportUrl, '_blank');
        }
        
        // Search functions
        function toggleSearch() {
            const searchForm = document.getElementById('searchForm');
            const button = event.target;

            if (searchForm.style.display === 'none') {
                searchForm.style.display = 'block';
                button.textContent = 'সার্চ লুকান';
            } else {
                searchForm.style.display = 'none';
                button.textContent = 'সার্চ অপশন';
            }
        }

        function submitSearch() {
            const form = document.querySelector('.search-form');
            const formData = new FormData(form);
            const params = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value.trim() !== '') {
                    params.append(key, value);
                }
            }

            window.location.href = '?' + params.toString();
        }

        function resetSearch() {
            window.location.href = window.location.pathname;
        }

        // Auto-submit on Enter key
        document.addEventListener('DOMContentLoaded', function() {
            const searchInputs = document.querySelectorAll('.search-input');
            searchInputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        submitSearch();
                    }
                });
            });
        });

        // Initialize
        updateSelectedCount();
    </script>
</body>
</html>
