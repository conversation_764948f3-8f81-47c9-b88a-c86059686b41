<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="student_upload_sample.csv"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Bengali characters in Excel)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Header
$header = [
    'student_id',
    'first_name', 
    'last_name',
    'class_name',
    'roll_number',
    'date_of_birth',
    'gender',
    'father_name',
    'mother_name',
    'guardian_name',
    'guardian_relation',
    'guardian_phone',
    'guardian_email',
    'guardian_address',
    'phone',
    'parent_phone',
    'address',
    'username',
    'email',
    'password'
];

// Write header
fputcsv($output, $header);

// Sample data rows
$sample_data = [
    [
        'STU2024001',
        'আহমেদ',
        'আলী',
        'Class 1',
        '1',
        '2010-05-15',
        'male',
        'মোহাম্মদ আলী',
        'ফাতেমা খাতুন',
        'মোহাম্মদ আলী',
        'পিতা',
        '01711111111',
        '<EMAIL>',
        'ঢাকা, বাংলাদেশ',
        '01811111111',
        '01711111111',
        'ঢাকা, বাংলাদেশ',
        'ahmed_ali',
        '<EMAIL>',
        'student123'
    ],
    [
        'STU2024002',
        'ফাতিমা',
        'বেগম',
        'Class 1',
        '2',
        '2010-08-20',
        'female',
        'আব্দুল করিম',
        'রাহেলা বেগম',
        'আব্দুল করিম',
        'পিতা',
        '01722222222',
        '<EMAIL>',
        'চট্টগ্রাম, বাংলাদেশ',
        '01822222222',
        '01722222222',
        'চট্টগ্রাম, বাংলাদেশ',
        'fatima_begum',
        '<EMAIL>',
        'student123'
    ],
    [
        'STU2024003',
        'রহিম',
        'উদ্দিন',
        'Class 2',
        '1',
        '2009-12-10',
        'male',
        'আব্দুর রহমান',
        'সালমা খাতুন',
        'আব্দুর রহমান',
        'পিতা',
        '01733333333',
        '<EMAIL>',
        'সিলেট, বাংলাদেশ',
        '01833333333',
        '01733333333',
        'সিলেট, বাংলাদেশ',
        'rahim_uddin',
        '<EMAIL>',
        'student123'
    ],
    [
        'STU2024004',
        'আয়েশা',
        'সিদ্দিকা',
        'Class 2',
        '2',
        '2009-03-25',
        'female',
        'মোহাম্মদ সিদ্দিক',
        'নাসরিন আক্তার',
        'নাসরিন আক্তার',
        'মাতা',
        '01744444444',
        '<EMAIL>',
        'রাজশাহী, বাংলাদেশ',
        '01844444444',
        '01744444444',
        'রাজশাহী, বাংলাদেশ',
        'ayesha_siddika',
        '<EMAIL>',
        'student123'
    ],
    [
        'STU2024005',
        'করিম',
        'মিয়া',
        'Class 3',
        '1',
        '2008-07-18',
        'male',
        'আব্দুল মজিদ',
        'রোকেয়া বেগম',
        'আব্দুল মজিদ',
        'পিতা',
        '01755555555',
        '<EMAIL>',
        'খুলনা, বাংলাদেশ',
        '01855555555',
        '01755555555',
        'খুলনা, বাংলাদেশ',
        'karim_mia',
        '<EMAIL>',
        'student123'
    ]
];

// Write sample data
foreach ($sample_data as $row) {
    fputcsv($output, $row);
}

// Close file pointer
fclose($output);
exit;
?>
