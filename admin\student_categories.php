<?php
require_once '../config/database.php';
checkRole('admin');

$pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USERNAME, DB_PASSWORD);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$message = '';
$error = '';

// Create student_categories table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS student_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category_name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            scholarship_eligible BOOLEAN DEFAULT FALSE,
            fee_discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Insert default categories if table is empty
    $count_check = $pdo->query("SELECT COUNT(*) FROM student_categories")->fetchColumn();
    if ($count_check == 0) {
        $default_categories = [
            ['সাধারণ', 'সাধারণ ক্যাটেগরির ছাত্র-ছাত্রী', 0, 0.00],
            ['মেধাবী', 'মেধাবী ছাত্র-ছাত্রী (৮০% এর উপরে)', 1, 25.00],
            ['দরিদ্র', 'অর্থনৈতিকভাবে দুর্বল পরিবারের সন্তান', 1, 50.00],
            ['এতিম', 'এতিম ছাত্র-ছাত্রী', 1, 75.00],
            ['প্রতিবন্ধী', 'শারীরিক বা মানসিক প্রতিবন্ধী', 1, 100.00],
            ['মুক্তিযোদ্ধা সন্তান', 'মুক্তিযোদ্ধার সন্তান/নাতি-নাতনি', 1, 100.00]
        ];
        
        $insert_stmt = $pdo->prepare("
            INSERT INTO student_categories (category_name, description, scholarship_eligible, fee_discount_percentage) 
            VALUES (?, ?, ?, ?)
        ");
        
        foreach ($default_categories as $category) {
            $insert_stmt->execute($category);
        }
    }
} catch(PDOException $e) {
    // Table might already exist
}

// Create student_category_assignments table if not exists
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS student_category_assignments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            category_id INT NOT NULL,
            assigned_date DATE DEFAULT (CURRENT_DATE),
            academic_year VARCHAR(10) DEFAULT '2024-25',
            notes TEXT,
            assigned_by INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES student_categories(id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_by) REFERENCES users(id),
            UNIQUE KEY unique_student_year (student_id, academic_year)
        )
    ");
} catch(PDOException $e) {
    // Table might already exist
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'];
        
        if ($action === 'add_category') {
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);
            $scholarship_eligible = isset($_POST['scholarship_eligible']) ? 1 : 0;
            $fee_discount = (float)($_POST['fee_discount_percentage'] ?? 0);
            
            if (empty($category_name)) {
                throw new Exception('ক্যাটেগরির নাম আবশ্যক।');
            }
            
            if ($fee_discount < 0 || $fee_discount > 100) {
                throw new Exception('ছাড়ের পরিমাণ ০ থেকে ১০০ এর মধ্যে হতে হবে।');
            }
            
            $insert_stmt = $pdo->prepare("
                INSERT INTO student_categories (category_name, description, scholarship_eligible, fee_discount_percentage) 
                VALUES (?, ?, ?, ?)
            ");
            $insert_stmt->execute([$category_name, $description, $scholarship_eligible, $fee_discount]);
            
            $message = 'নতুন ক্যাটেগরি সফলভাবে যোগ করা হয়েছে!';
            
        } elseif ($action === 'edit_category') {
            $category_id = (int)$_POST['category_id'];
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);
            $scholarship_eligible = isset($_POST['scholarship_eligible']) ? 1 : 0;
            $fee_discount = (float)($_POST['fee_discount_percentage'] ?? 0);
            
            if (empty($category_name)) {
                throw new Exception('ক্যাটেগরির নাম আবশ্যক।');
            }
            
            if ($fee_discount < 0 || $fee_discount > 100) {
                throw new Exception('ছাড়ের পরিমাণ ০ থেকে ১০০ এর মধ্যে হতে হবে।');
            }
            
            $update_stmt = $pdo->prepare("
                UPDATE student_categories 
                SET category_name = ?, description = ?, scholarship_eligible = ?, fee_discount_percentage = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $update_stmt->execute([$category_name, $description, $scholarship_eligible, $fee_discount, $category_id]);
            
            $message = 'ক্যাটেগরি সফলভাবে আপডেট করা হয়েছে!';
            
        } elseif ($action === 'delete_category') {
            $category_id = (int)$_POST['category_id'];
            
            // Check if category is assigned to any students
            $assignment_check = $pdo->prepare("SELECT COUNT(*) as count FROM student_category_assignments WHERE category_id = ? AND is_active = 1");
            $assignment_check->execute([$category_id]);
            $assignment_count = $assignment_check->fetch()['count'];
            
            if ($assignment_count > 0) {
                throw new Exception('এই ক্যাটেগরি ছাত্র-ছাত্রীদের নিয়োগ করা আছে। প্রথমে সেগুলি সরান।');
            }
            
            $delete_stmt = $pdo->prepare("DELETE FROM student_categories WHERE id = ?");
            $delete_stmt->execute([$category_id]);
            
            $message = 'ক্যাটেগরি সফলভাবে মুছে ফেলা হয়েছে!';
            
        } elseif ($action === 'assign_category') {
            $student_id = (int)$_POST['student_id'];
            $category_id = (int)$_POST['category_id'];
            $academic_year = $_POST['academic_year'] ?? '2024-25';
            $notes = trim($_POST['notes']);
            
            if (empty($student_id) || empty($category_id)) {
                throw new Exception('ছাত্র এবং ক্যাটেগরি নির্বাচন করুন।');
            }
            
            // Remove existing assignment for this student and year
            $remove_stmt = $pdo->prepare("UPDATE student_category_assignments SET is_active = 0 WHERE student_id = ? AND academic_year = ?");
            $remove_stmt->execute([$student_id, $academic_year]);
            
            // Add new assignment
            $assign_stmt = $pdo->prepare("
                INSERT INTO student_category_assignments (student_id, category_id, academic_year, notes, assigned_by) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $assign_stmt->execute([$student_id, $category_id, $academic_year, $notes, $_SESSION['user_id']]);
            
            $message = 'ছাত্রের ক্যাটেগরি সফলভাবে নিয়োগ করা হয়েছে!';
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all categories
$categories = $pdo->query("
    SELECT sc.*, 
           COUNT(sca.id) as assigned_students
    FROM student_categories sc
    LEFT JOIN student_category_assignments sca ON sc.id = sca.category_id AND sca.is_active = 1
    WHERE sc.is_active = 1
    GROUP BY sc.id
    ORDER BY sc.category_name
")->fetchAll();

// Get all students for assignment
$students = $pdo->query("
    SELECT s.id, s.name, s.roll_number, c.class_name, c.section
    FROM students s
    JOIN classes c ON s.class_id = c.id
    WHERE s.is_active = 1
    ORDER BY c.class_name, c.section, s.roll_number
")->fetchAll();

// Get student assignments with details
$assignments = $pdo->query("
    SELECT sca.*, s.name as student_name, s.roll_number, 
           c.class_name, c.section, sc.category_name, sc.scholarship_eligible, sc.fee_discount_percentage,
           u.username as assigned_by_name
    FROM student_category_assignments sca
    JOIN students s ON sca.student_id = s.id
    JOIN classes c ON s.class_id = c.id
    JOIN student_categories sc ON sca.category_id = sc.id
    LEFT JOIN users u ON sca.assigned_by = u.id
    WHERE sca.is_active = 1
    ORDER BY c.class_name, c.section, s.roll_number
")->fetchAll();

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র ক্যাটেগরি ও উপবৃত্তি - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .categories-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .page-header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin: 0;
        }
        
        .stat-card p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .category-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        
        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .category-card.scholarship {
            border-left-color: #28a745;
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .category-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }
        
        .scholarship-badge {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .discount-badge {
            background: #ffc107;
            color: #333;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .category-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn-action {
            padding: 0.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-edit {
            background: #007bff;
            color: white;
        }
        
        .btn-edit:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
            transform: translateY(-1px);
        }
        
        .assignment-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .assignment-table th,
        .assignment-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .assignment-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .assignment-table tr:hover {
            background: #f8f9fa;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #007bff;
            outline: none;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #f1f3f4;
            padding-bottom: 1rem;
        }
        
        .modal-title {
            margin: 0;
            color: #333;
            font-size: 1.5rem;
        }
        
        .close {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <?php include '../includes/sidebar.php'; ?>
    
    <div class="categories-container">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div style="margin-bottom: 1rem;">
                    <a href="dashboard.php" class="btn btn-primary" style="background: rgba(255,255,255,0.2); border: 2px solid white;">
                        <i class="fas fa-arrow-left"></i> ড্যাশবোর্ডে ফিরুন
                    </a>
                </div>
                <h1><i class="fas fa-tags"></i> ছাত্র ক্যাটেগরি ও উপবৃত্তি ব্যবস্থাপনা</h1>
                <p>ছাত্র-ছাত্রীদের ক্যাটেগরি নির্ধারণ ও উপবৃত্তি প্রদান ব্যবস্থাপনা</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php echo count($categories); ?></h3>
                    <p>মোট ক্যাটেগরি</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo count(array_filter($categories, function($c) { return $c['scholarship_eligible']; })); ?></h3>
                    <p>উপবৃত্তি যোগ্য</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo count($assignments); ?></h3>
                    <p>নিয়োগকৃত ছাত্র</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo count($students); ?></h3>
                    <p>মোট ছাত্র</p>
                </div>
            </div>

            <!-- Category Management -->
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <h2 style="margin: 0;"><i class="fas fa-tags"></i> ক্যাটেগরি ব্যবস্থাপনা</h2>
                    <button type="button" class="btn btn-primary" onclick="showAddCategoryModal()">
                        <i class="fas fa-plus-circle"></i> নতুন ক্যাটেগরি যোগ করুন
                    </button>
                </div>

                <div class="category-grid">
                    <?php foreach ($categories as $category): ?>
                        <div class="category-card <?php echo $category['scholarship_eligible'] ? 'scholarship' : ''; ?>">
                            <div class="category-header">
                                <div class="category-title">
                                    <i class="fas fa-tag"></i> <?php echo htmlspecialchars($category['category_name']); ?>
                                </div>
                                <div>
                                    <?php if ($category['scholarship_eligible']): ?>
                                        <span class="scholarship-badge">
                                            <i class="fas fa-graduation-cap"></i> উপবৃত্তি
                                        </span>
                                    <?php endif; ?>
                                    <?php if ($category['fee_discount_percentage'] > 0): ?>
                                        <span class="discount-badge">
                                            <?php echo $category['fee_discount_percentage']; ?>% ছাড়
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <p style="color: #666; margin-bottom: 1rem;">
                                <?php echo htmlspecialchars($category['description'] ?: 'কোন বিবরণ নেই'); ?>
                            </p>

                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #007bff; font-weight: bold;">
                                    <i class="fas fa-users"></i> <?php echo $category['assigned_students']; ?> জন নিয়োগকৃত
                                </span>
                                <div class="category-actions">
                                    <button type="button" class="btn-action btn-edit"
                                            onclick="editCategory(<?php echo $category['id']; ?>, '<?php echo addslashes($category['category_name']); ?>', '<?php echo addslashes($category['description']); ?>', <?php echo $category['scholarship_eligible']; ?>, <?php echo $category['fee_discount_percentage']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn-action btn-delete"
                                            onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo addslashes($category['category_name']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Student Assignment -->
            <div class="card">
                <h2><i class="fas fa-user-tag"></i> ছাত্র ক্যাটেগরি নিয়োগ</h2>

                <form method="POST" style="margin-bottom: 2rem;">
                    <input type="hidden" name="action" value="assign_category">

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">ছাত্র নির্বাচন করুন <span style="color: red;">*</span></label>
                            <select name="student_id" class="form-control" required>
                                <option value="">-- ছাত্র নির্বাচন করুন --</option>
                                <?php foreach ($students as $student): ?>
                                    <option value="<?php echo $student['id']; ?>">
                                        <?php echo htmlspecialchars($student['name']); ?>
                                        (রোল: <?php echo $student['roll_number']; ?>) -
                                        <?php echo htmlspecialchars($student['class_name']); ?>
                                        <?php echo htmlspecialchars($student['section']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">ক্যাটেগরি নির্বাচন করুন <span style="color: red;">*</span></label>
                            <select name="category_id" class="form-control" required>
                                <option value="">-- ক্যাটেগরি নির্বাচন করুন --</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['category_name']); ?>
                                        <?php if ($category['scholarship_eligible']): ?>
                                            (উপবৃত্তি: <?php echo $category['fee_discount_percentage']; ?>% ছাড়)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">শিক্ষাবর্ষ</label>
                            <select name="academic_year" class="form-control">
                                <option value="2024-25">২০২৪-২৫</option>
                                <option value="2023-24">২০২৩-২৪</option>
                                <option value="2025-26">২০২৫-২৬</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">মন্তব্য</label>
                            <textarea name="notes" class="form-control" rows="2" placeholder="প্রয়োজনে কোন মন্তব্য লিখুন"></textarea>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> ক্যাটেগরি নিয়োগ করুন
                    </button>
                </form>
            </div>

            <!-- Current Assignments -->
            <div class="card">
                <h2><i class="fas fa-list"></i> বর্তমান নিয়োগসমূহ</h2>

                <?php if (count($assignments) > 0): ?>
                    <div style="overflow-x: auto;">
                        <table class="assignment-table">
                            <thead>
                                <tr>
                                    <th>ছাত্রের নাম</th>
                                    <th>রোল নম্বর</th>
                                    <th>ক্লাস</th>
                                    <th>ক্যাটেগরি</th>
                                    <th>উপবৃত্তি</th>
                                    <th>ছাড়</th>
                                    <th>নিয়োগের তারিখ</th>
                                    <th>শিক্ষাবর্ষ</th>
                                    <th>নিয়োগকারী</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assignments as $assignment): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($assignment['student_name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($assignment['roll_number']); ?></td>
                                        <td><?php echo htmlspecialchars($assignment['class_name'] . ' ' . $assignment['section']); ?></td>
                                        <td>
                                            <span style="background: #e9ecef; padding: 0.25rem 0.5rem; border-radius: 5px;">
                                                <?php echo htmlspecialchars($assignment['category_name']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($assignment['scholarship_eligible']): ?>
                                                <span style="color: #28a745; font-weight: bold;">
                                                    <i class="fas fa-check-circle"></i> হ্যাঁ
                                                </span>
                                            <?php else: ?>
                                                <span style="color: #6c757d;">
                                                    <i class="fas fa-times-circle"></i> না
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($assignment['fee_discount_percentage'] > 0): ?>
                                                <span style="background: #ffc107; color: #333; padding: 0.25rem 0.5rem; border-radius: 5px; font-weight: bold;">
                                                    <?php echo $assignment['fee_discount_percentage']; ?>%
                                                </span>
                                            <?php else: ?>
                                                <span style="color: #6c757d;">০%</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y', strtotime($assignment['assigned_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($assignment['academic_year']); ?></td>
                                        <td><?php echo htmlspecialchars($assignment['assigned_by_name'] ?: 'অজানা'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div style="text-align: center; padding: 3rem; color: #6c757d;">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                        <h3>কোন নিয়োগ পাওয়া যায়নি</h3>
                        <p>এখনও কোন ছাত্রকে ক্যাটেগরি নিয়োগ করা হয়নি।</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div id="addCategoryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus-circle" style="color: #007bff;"></i> নতুন ক্যাটেগরি যোগ করুন
                </h3>
                <button type="button" class="close" onclick="closeAddCategoryModal()">&times;</button>
            </div>

            <form method="POST" id="addCategoryForm">
                <input type="hidden" name="action" value="add_category">

                <div class="form-group">
                    <label class="form-label">ক্যাটেগরির নাম <span style="color: red;">*</span></label>
                    <input type="text" name="category_name" class="form-control" placeholder="যেমন: মেধাবী, দরিদ্র, এতিম" required>
                </div>

                <div class="form-group">
                    <label class="form-label">বিবরণ</label>
                    <textarea name="description" class="form-control" rows="3" placeholder="এই ক্যাটেগরি সম্পর্কে বিস্তারিত লিখুন"></textarea>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" name="scholarship_eligible" id="scholarship_eligible" value="1">
                        <label for="scholarship_eligible">উপবৃত্তি যোগ্য</label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">ফি ছাড়ের পরিমাণ (%)</label>
                    <input type="number" name="fee_discount_percentage" class="form-control" min="0" max="100" step="0.01" value="0" placeholder="০ থেকে ১০০">
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeAddCategoryModal()" style="margin-right: 1rem;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i> ক্যাটেগরি যোগ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="editCategoryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-edit" style="color: #007bff;"></i> ক্যাটেগরি সম্পাদনা
                </h3>
                <button type="button" class="close" onclick="closeEditCategoryModal()">&times;</button>
            </div>

            <form method="POST" id="editCategoryForm">
                <input type="hidden" name="action" value="edit_category">
                <input type="hidden" name="category_id" id="edit_category_id">

                <div class="form-group">
                    <label class="form-label">ক্যাটেগরির নাম <span style="color: red;">*</span></label>
                    <input type="text" name="category_name" id="edit_category_name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label class="form-label">বিবরণ</label>
                    <textarea name="description" id="edit_category_description" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" name="scholarship_eligible" id="edit_scholarship_eligible" value="1">
                        <label for="edit_scholarship_eligible">উপবৃত্তি যোগ্য</label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">ফি ছাড়ের পরিমাণ (%)</label>
                    <input type="number" name="fee_discount_percentage" id="edit_fee_discount" class="form-control" min="0" max="100" step="0.01">
                </div>

                <div style="text-align: center; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeEditCategoryModal()" style="margin-right: 1rem;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> পরিবর্তন সংরক্ষণ করুন
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Category Modal -->
    <div id="deleteCategoryModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-trash" style="color: #dc3545;"></i> ক্যাটেগরি মুছে ফেলুন
                </h3>
                <button type="button" class="close" onclick="closeDeleteCategoryModal()">&times;</button>
            </div>

            <div style="text-align: center; padding: 2rem;">
                <i class="fas fa-exclamation-triangle" style="font-size: 4rem; color: #ffc107; margin-bottom: 1rem;"></i>
                <h4>আপনি কি নিশ্চিত?</h4>
                <p>আপনি "<span id="delete_category_name"></span>" ক্যাটেগরি মুছে ফেলতে চান?</p>
                <p style="color: #dc3545; font-weight: bold;">এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না!</p>
            </div>

            <form method="POST" id="deleteCategoryForm">
                <input type="hidden" name="action" value="delete_category">
                <input type="hidden" name="category_id" id="delete_category_id">

                <div style="text-align: center;">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteCategoryModal()" style="margin-right: 1rem;">
                        <i class="fas fa-times"></i> বাতিল
                    </button>
                    <button type="submit" class="btn" style="background: #dc3545; color: white;">
                        <i class="fas fa-trash"></i> হ্যাঁ, মুছে ফেলুন
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Add Category Modal Functions
        function showAddCategoryModal() {
            document.getElementById('addCategoryModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeAddCategoryModal() {
            document.getElementById('addCategoryModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('addCategoryForm').reset();
        }

        // Edit Category Modal Functions
        function editCategory(categoryId, categoryName, description, scholarshipEligible, feeDiscount) {
            document.getElementById('edit_category_id').value = categoryId;
            document.getElementById('edit_category_name').value = categoryName;
            document.getElementById('edit_category_description').value = description || '';
            document.getElementById('edit_scholarship_eligible').checked = scholarshipEligible == 1;
            document.getElementById('edit_fee_discount').value = feeDiscount;
            document.getElementById('editCategoryModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeEditCategoryModal() {
            document.getElementById('editCategoryModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Delete Category Modal Functions
        function deleteCategory(categoryId, categoryName) {
            document.getElementById('delete_category_id').value = categoryId;
            document.getElementById('delete_category_name').textContent = categoryName;
            document.getElementById('deleteCategoryModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeDeleteCategoryModal() {
            document.getElementById('deleteCategoryModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const addModal = document.getElementById('addCategoryModal');
            const editModal = document.getElementById('editCategoryModal');
            const deleteModal = document.getElementById('deleteCategoryModal');

            if (event.target === addModal) {
                closeAddCategoryModal();
            } else if (event.target === editModal) {
                closeEditCategoryModal();
            } else if (event.target === deleteModal) {
                closeDeleteCategoryModal();
            }
        }
    </script>
</body>
</html>
