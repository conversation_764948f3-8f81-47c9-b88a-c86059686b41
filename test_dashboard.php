<?php
// Simple test for dashboard without session requirements
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্যাশবোর্ড টেস্ট - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card.students { border-left-color: #3498db; }
        .stat-card.teachers { border-left-color: #2ecc71; }
        .stat-card.classes { border-left-color: #f39c12; }
        .stat-card.subjects { border-left-color: #e74c3c; }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .students .stat-icon { color: #3498db; }
        .teachers .stat-icon { color: #2ecc71; }
        .classes .stat-icon { color: #f39c12; }
        .subjects .stat-icon { color: #e74c3c; }
        
        .students .stat-number { color: #3498db; }
        .teachers .stat-number { color: #2ecc71; }
        .classes .stat-number { color: #f39c12; }
        .subjects .stat-number { color: #e74c3c; }
        
        .recent-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-title {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .welcome-subtitle {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">🏫 স্কুল ব্যবস্থাপনা</h2>
                <button class="mobile-toggle" onclick="toggleSidebar()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">📊 ড্যাশবোর্ড</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/dashboard.php" class="nav-link active">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">মূল ড্যাশবোর্ড</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/analytics.php" class="nav-link">
                                <span class="nav-icon">📈</span>
                                <span class="nav-text">বিশ্লেষণ</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Student Management Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">👨‍🎓 ছাত্র ব্যবস্থাপনা</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/students.php" class="nav-link">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">ছাত্র তালিকা</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/admission.php" class="nav-link">
                                <span class="nav-icon">📝</span>
                                <span class="nav-text">নতুন ভর্তি</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/attendance.php" class="nav-link">
                                <span class="nav-icon">📅</span>
                                <span class="nav-text">উপস্থিতি</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Teacher Management Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">👩‍🏫 শিক্ষক ব্যবস্থাপনা</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/teachers.php" class="nav-link">
                                <span class="nav-icon">👨‍🏫</span>
                                <span class="nav-text">শিক্ষক তালিকা</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/teacher_schedule.php" class="nav-link">
                                <span class="nav-icon">🗓️</span>
                                <span class="nav-text">ক্লাস রুটিন</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Accounting Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">💰 একাউন্টিং</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/fee_collection.php" class="nav-link">
                                <span class="nav-icon">💳</span>
                                <span class="nav-text">ফি সংগ্রহ</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/expenses.php" class="nav-link">
                                <span class="nav-icon">💸</span>
                                <span class="nav-text">খরচ ব্যবস্থাপনা</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Exam Management Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">📝 পরীক্ষা ব্যবস্থাপনা</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/exams.php" class="nav-link">
                                <span class="nav-icon">📄</span>
                                <span class="nav-text">পরীক্ষা তালিকা</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/marks_entry.php" class="nav-link">
                                <span class="nav-icon">✏️</span>
                                <span class="nav-text">নম্বর এন্ট্রি</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Managing Committee Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">👥 ম্যানেজিং কমিটি</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/committee_members.php" class="nav-link">
                                <span class="nav-icon">👨‍💼</span>
                                <span class="nav-text">কমিটি সদস্য</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/meetings.php" class="nav-link">
                                <span class="nav-icon">🤝</span>
                                <span class="nav-text">সভা ব্যবস্থাপনা</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">⚙️ সেটিংস</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="admin/school_settings.php" class="nav-link">
                                <span class="nav-icon">🏫</span>
                                <span class="nav-text">স্কুল সেটিংস</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="admin/user_management.php" class="nav-link">
                                <span class="nav-icon">👤</span>
                                <span class="nav-text">ইউজার ব্যবস্থাপনা</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="sidebar-footer">
                <a href="logout.php" class="logout-btn">
                    <span class="nav-icon">🚪</span>
                    <span class="nav-text">লগআউট</span>
                </a>
            </div>
        </div>
        
        <div class="sidebar-overlay" onclick="toggleSidebar()"></div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">ড্যাশবোর্ড</h1>
                <p class="content-subtitle">স্কুল ব্যবস্থাপনা সিস্টেমের সার্বিক অবস্থা</p>
            </div>
            
            <div class="content-body">
                <!-- Welcome Card -->
                <div class="welcome-card">
                    <h2 class="welcome-title">স্বাগতম, অ্যাডমিন!</h2>
                    <p class="welcome-subtitle">আজ <?php echo date('d F Y, l'); ?> - আপনার স্কুল ব্যবস্থাপনা ড্যাশবোর্ডে স্বাগতম</p>
                </div>

                <!-- Statistics Cards -->
                <div class="dashboard-grid">
                    <div class="stat-card students">
                        <div class="stat-icon">👨‍🎓</div>
                        <div class="stat-number">150</div>
                        <div class="stat-label">মোট ছাত্র</div>
                    </div>
                    
                    <div class="stat-card teachers">
                        <div class="stat-icon">👩‍🏫</div>
                        <div class="stat-number">25</div>
                        <div class="stat-label">মোট শিক্ষক</div>
                    </div>
                    
                    <div class="stat-card classes">
                        <div class="stat-icon">🏫</div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">মোট ক্লাস</div>
                    </div>
                    
                    <div class="stat-card subjects">
                        <div class="stat-icon">📚</div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">মোট বিষয়</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="recent-section">
                    <h3 class="section-title">⚡ দ্রুত কাজ</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        <a href="admin/students.php" class="btn btn-primary" style="text-decoration: none; text-align: center; padding: 1rem;">
                            👨‍🎓 ছাত্র ব্যবস্থাপনা
                        </a>
                        <a href="admin/teachers.php" class="btn btn-success" style="text-decoration: none; text-align: center; padding: 1rem;">
                            👩‍🏫 শিক্ষক ব্যবস্থাপনা
                        </a>
                        <a href="admin/classes.php" class="btn btn-warning" style="text-decoration: none; text-align: center; padding: 1rem;">
                            🏫 ক্লাস ব্যবস্থাপনা
                        </a>
                        <a href="admin/attendance.php" class="btn btn-info" style="text-decoration: none; text-align: center; padding: 1rem;">
                            📅 উপস্থিতি নিন
                        </a>
                    </div>
                </div>

                <!-- Recent Students -->
                <div class="recent-section">
                    <h3 class="section-title">🆕 সাম্প্রতিক ভর্তি হওয়া ছাত্রগণ</h3>
                    <p style="text-align: center; color: #6c757d; padding: 2rem;">ডেমো ডেটা - প্রকৃত ডেটাবেস সংযোগের জন্য লগইন করুন</p>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // Sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }
        
        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            
            // Add some interactive effects
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Add click animation
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-5px)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
