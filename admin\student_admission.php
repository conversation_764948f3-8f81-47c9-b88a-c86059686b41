<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get form data
        $student_id = trim($_POST['student_id']);
        $first_name = trim($_POST['first_name']);
        $last_name = trim($_POST['last_name']);
        $class_id = (int)$_POST['class_id'];
        $roll_number = (int)$_POST['roll_number'];
        $date_of_birth = $_POST['date_of_birth'] ?: null;
        $gender = $_POST['gender'] ?: null;
        $father_name = trim($_POST['father_name']);
        $mother_name = trim($_POST['mother_name']);
        $guardian_name = trim($_POST['guardian_name']);
        $guardian_relation = trim($_POST['guardian_relation']);
        $guardian_phone = trim($_POST['guardian_phone']);
        $guardian_email = trim($_POST['guardian_email']);
        $guardian_address = trim($_POST['guardian_address']);
        $phone = trim($_POST['phone']);
        $parent_phone = trim($_POST['parent_phone']);
        $address = trim($_POST['address']);
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $password = $_POST['password'] ?: 'student123';
        
        // Validate required fields
        if (empty($student_id) || empty($first_name) || empty($last_name) || 
            empty($class_id) || empty($username) || empty($email)) {
            throw new Exception('সব বাধ্যতামূলক ফিল্ড পূরণ করুন।');
        }
        
        // Check if student ID already exists
        $stmt = $pdo->prepare("SELECT id FROM students WHERE student_id = ?");
        $stmt->execute([$student_id]);
        if ($stmt->fetch()) {
            throw new Exception('এই ছাত্র ID ইতিমধ্যে ব্যবহৃত হয়েছে।');
        }
        
        // Check if username already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->fetch()) {
            throw new Exception('এই ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে।');
        }
        
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            throw new Exception('এই ইমেইল ইতিমধ্যে ব্যবহৃত হয়েছে।');
        }
        
        // Handle photo upload
        $photo_path = '';
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
            $photo_path = uploadFile($_FILES['photo'], 'uploads/students/');
        }
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Insert user
        $hashed_password = hashPassword($password);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'student')");
        $stmt->execute([$username, $hashed_password, $email]);
        $user_id = $pdo->lastInsertId();
        
        // Insert student
        $stmt = $pdo->prepare("
            INSERT INTO students (
                user_id, student_id, first_name, last_name, class_id, roll_number, 
                date_of_birth, gender, photo, father_name, mother_name, guardian_name, 
                guardian_relation, guardian_phone, guardian_email, guardian_address, 
                phone, parent_phone, address, admission_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())
        ");
        
        $stmt->execute([
            $user_id, $student_id, $first_name, $last_name, $class_id, $roll_number,
            $date_of_birth, $gender, $photo_path, $father_name, $mother_name, 
            $guardian_name, $guardian_relation, $guardian_phone, $guardian_email,
            $guardian_address, $phone, $parent_phone, $address
        ]);
        
        $pdo->commit();
        $message = 'ছাত্র সফলভাবে ভর্তি হয়েছে!';
        
        // Clear form data
        $_POST = [];
        
    } catch (Exception $e) {
        $pdo->rollback();
        $error = $e->getMessage();
    }
}

// Get classes for dropdown
try {
    $stmt = $pdo->query("SELECT * FROM classes ORDER BY class_name");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $classes = [];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র ভর্তি - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admission-container {
            max-width: 1000px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .admission-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .admission-title {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .admission-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .form-sections {
            display: grid;
            gap: 2rem;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .form-grid-full {
            grid-column: 1 / -1;
        }
        
        .photo-upload {
            text-align: center;
            padding: 2rem;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
        }
        
        .photo-upload:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .photo-preview {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 1rem;
            display: block;
            border: 4px solid #e9ecef;
        }
        
        .submit-section {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #e9ecef;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 3rem;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        
        .required-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .required {
            color: #d32f2f;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">📝 নতুন ছাত্র ভর্তি</h1>
                <p class="content-subtitle">নতুন ছাত্রের সম্পূর্ণ তথ্য দিয়ে ভর্তি করুন</p>
            </div>
            
            <div class="content-body">
                <div class="admission-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Required Fields Note -->
                    <div class="required-note">
                        <strong>📋 নোট:</strong> <span class="required">*</span> চিহ্নিত ফিল্ডগুলো বাধ্যতামূলক। অন্যান্য ফিল্ড ঐচ্ছিক।
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data" class="admission-form">
                        <div class="form-sections">
                            <!-- Basic Information -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    👤 মৌলিক তথ্য
                                </h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">ছাত্র ID <span class="required">*</span></label>
                                        <input type="text" name="student_id" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['student_id'] ?? ''); ?>" 
                                               placeholder="STU2024001" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">নাম <span class="required">*</span></label>
                                        <input type="text" name="first_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" 
                                               placeholder="আহমেদ" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">পদবি <span class="required">*</span></label>
                                        <input type="text" name="last_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" 
                                               placeholder="আলী" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">ক্লাস <span class="required">*</span></label>
                                        <select name="class_id" class="form-control" required>
                                            <option value="">ক্লাস নির্বাচন করুন</option>
                                            <?php foreach ($classes as $class): ?>
                                                <option value="<?php echo $class['id']; ?>" 
                                                        <?php echo (isset($_POST['class_id']) && $_POST['class_id'] == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">রোল নম্বর</label>
                                        <input type="number" name="roll_number" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['roll_number'] ?? ''); ?>" 
                                               placeholder="1" min="1">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">জন্ম তারিখ</label>
                                        <input type="date" name="date_of_birth" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['date_of_birth'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">লিঙ্গ</label>
                                        <select name="gender" class="form-control">
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="male" <?php echo (isset($_POST['gender']) && $_POST['gender'] == 'male') ? 'selected' : ''; ?>>পুরুষ</option>
                                            <option value="female" <?php echo (isset($_POST['gender']) && $_POST['gender'] == 'female') ? 'selected' : ''; ?>>মহিলা</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Photo Upload -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    📷 ছবি আপলোড
                                </h3>
                                <div class="photo-upload">
                                    <div id="photoPreview" class="photo-preview" style="display: none;">
                                        <img id="previewImage" style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 4px solid #e9ecef;" alt="ছবি প্রিভিউ">
                                    </div>
                                    <div id="uploadPlaceholder">
                                        <div style="font-size: 3rem; margin-bottom: 1rem;">📷</div>
                                        <p>ছাত্রের ছবি আপলোড করুন</p>
                                        <p style="font-size: 0.9rem; color: #6c757d;">JPG, PNG, GIF (সর্বোচ্চ 2MB)</p>
                                    </div>
                                    <input type="file" name="photo" id="photoInput" accept="image/*"
                                           style="display: none;" onchange="previewPhoto(this)">
                                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('photoInput').click()">
                                        ছবি নির্বাচন করুন
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <button type="button" class="btn btn-info" onclick="toggleOptionalSections()">
                                📋 অতিরিক্ত তথ্য যোগ করুন (ঐচ্ছিক)
                            </button>
                        </div>
                        
                        <!-- Optional Sections (Hidden by default) -->
                        <div id="optionalSections" style="display: none; margin-top: 2rem;">
                            <!-- Family Information -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    👨‍👩‍👧‍👦 পারিবারিক তথ্য
                                </h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">পিতার নাম</label>
                                        <input type="text" name="father_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['father_name'] ?? ''); ?>" 
                                               placeholder="মোহাম্মদ আলী">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">মাতার নাম</label>
                                        <input type="text" name="mother_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['mother_name'] ?? ''); ?>" 
                                               placeholder="ফাতেমা খাতুন">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">অভিভাবকের নাম</label>
                                        <input type="text" name="guardian_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['guardian_name'] ?? ''); ?>" 
                                               placeholder="মোহাম্মদ আলী">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">অভিভাবকের সম্পর্ক</label>
                                        <select name="guardian_relation" class="form-control">
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="পিতা" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'পিতা') ? 'selected' : ''; ?>>পিতা</option>
                                            <option value="মাতা" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'মাতা') ? 'selected' : ''; ?>>মাতা</option>
                                            <option value="দাদা" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'দাদা') ? 'selected' : ''; ?>>দাদা</option>
                                            <option value="দাদী" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'দাদী') ? 'selected' : ''; ?>>দাদী</option>
                                            <option value="নানা" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'নানা') ? 'selected' : ''; ?>>নানা</option>
                                            <option value="নানী" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'নানী') ? 'selected' : ''; ?>>নানী</option>
                                            <option value="চাচা" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'চাচা') ? 'selected' : ''; ?>>চাচা</option>
                                            <option value="মামা" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'মামা') ? 'selected' : ''; ?>>মামা</option>
                                            <option value="অন্যান্য" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] == 'অন্যান্য') ? 'selected' : ''; ?>>অন্যান্য</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Contact Information -->
                            <div class="form-section">
                                <h3 class="section-title">
                                    📞 যোগাযোগের তথ্য
                                </h3>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">অভিভাবকের ফোন</label>
                                        <input type="tel" name="guardian_phone" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['guardian_phone'] ?? ''); ?>" 
                                               placeholder="01711111111">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">অভিভাবকের ইমেইল</label>
                                        <input type="email" name="guardian_email" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['guardian_email'] ?? ''); ?>" 
                                               placeholder="<EMAIL>">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">ছাত্রের ফোন</label>
                                        <input type="tel" name="phone" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                               placeholder="01811111111">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label">পিতা-মাতার ফোন</label>
                                        <input type="tel" name="parent_phone" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['parent_phone'] ?? ''); ?>" 
                                               placeholder="01711111111">
                                    </div>
                                    
                                    <div class="form-group form-grid-full">
                                        <label class="form-label">অভিভাবকের ঠিকানা</label>
                                        <textarea name="guardian_address" class="form-control" rows="3" 
                                                  placeholder="সম্পূর্ণ ঠিকানা লিখুন"><?php echo htmlspecialchars($_POST['guardian_address'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="form-group form-grid-full">
                                        <label class="form-label">ছাত্রের ঠিকানা</label>
                                        <textarea name="address" class="form-control" rows="3" 
                                                  placeholder="সম্পূর্ণ ঠিকানা লিখুন"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Login Information -->
                        <div class="form-section" style="margin-top: 2rem;">
                            <h3 class="section-title">
                                🔐 লগইন তথ্য
                            </h3>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">ইউজারনেম <span class="required">*</span></label>
                                    <input type="text" name="username" class="form-control" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                           placeholder="ahmed_ali" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">ইমেইল <span class="required">*</span></label>
                                    <input type="email" name="email" class="form-control" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           placeholder="<EMAIL>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">পাসওয়ার্ড</label>
                                    <input type="password" name="password" class="form-control" 
                                           placeholder="খালি রাখলে 'student123' হবে">
                                    <small class="form-text">খালি রাখলে ডিফল্ট পাসওয়ার্ড 'student123' ব্যবহার হবে</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="submit-section">
                            <button type="submit" class="submit-btn">
                                ✅ ছাত্র ভর্তি করুন
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script>
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImage').src = e.target.result;
                    document.getElementById('photoPreview').style.display = 'block';
                    document.getElementById('uploadPlaceholder').style.display = 'none';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        function toggleOptionalSections() {
            const sections = document.getElementById('optionalSections');
            const button = event.target;
            
            if (sections.style.display === 'none') {
                sections.style.display = 'block';
                button.innerHTML = '📋 অতিরিক্ত তথ্য লুকান';
            } else {
                sections.style.display = 'none';
                button.innerHTML = '📋 অতিরিক্ত তথ্য যোগ করুন (ঐচ্ছিক)';
            }
        }
        
        // Auto-generate username from name
        document.querySelector('input[name="first_name"]').addEventListener('input', generateUsername);
        document.querySelector('input[name="last_name"]').addEventListener('input', generateUsername);
        
        function generateUsername() {
            const firstName = document.querySelector('input[name="first_name"]').value.trim();
            const lastName = document.querySelector('input[name="last_name"]').value.trim();
            const usernameField = document.querySelector('input[name="username"]');
            
            if (firstName && lastName && !usernameField.value) {
                // Convert Bengali to English transliteration (simplified)
                const username = (firstName + '_' + lastName).toLowerCase()
                    .replace(/[^\w]/g, '_')
                    .replace(/_+/g, '_')
                    .replace(/^_|_$/g, '');
                usernameField.value = username;
            }
        }
        
        // Auto-generate email from username
        document.querySelector('input[name="username"]').addEventListener('input', function() {
            const username = this.value.trim();
            const emailField = document.querySelector('input[name="email"]');
            
            if (username && !emailField.value) {
                emailField.value = username + '@student.com';
            }
        });
    </script>
</body>
</html>
