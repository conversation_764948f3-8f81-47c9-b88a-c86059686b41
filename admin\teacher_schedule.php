<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_schedule') {
        try {
            $teacher_id = (int)$_POST['teacher_id'];
            $subject_id = (int)$_POST['subject_id'];
            $class_id = (int)$_POST['class_id'];
            $day_of_week = $_POST['day_of_week'];
            $start_time = $_POST['start_time'];
            $end_time = $_POST['end_time'];
            $room_number = trim($_POST['room_number']);
            
            // Validate required fields
            if (empty($teacher_id) || empty($subject_id) || empty($class_id) || 
                empty($day_of_week) || empty($start_time) || empty($end_time)) {
                throw new Exception('সব বাধ্যতামূলক ফিল্ড পূরণ করুন।');
            }
            
            // Check for time conflicts
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as conflicts 
                FROM teacher_schedules 
                WHERE teacher_id = ? AND day_of_week = ? 
                AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))
            ");
            $stmt->execute([$teacher_id, $day_of_week, $start_time, $start_time, $end_time, $end_time]);
            $conflicts = $stmt->fetch(PDO::FETCH_ASSOC)['conflicts'];
            
            if ($conflicts > 0) {
                throw new Exception('এই সময়ে শিক্ষকের অন্য ক্লাস আছে।');
            }
            
            // Insert schedule
            $stmt = $pdo->prepare("
                INSERT INTO teacher_schedules (teacher_id, subject_id, class_id, day_of_week, start_time, end_time, room_number)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$teacher_id, $subject_id, $class_id, $day_of_week, $start_time, $end_time, $room_number]);
            
            $message = 'সময়সূচী সফলভাবে যোগ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'delete_schedule') {
        try {
            $schedule_id = (int)$_POST['schedule_id'];
            
            $stmt = $pdo->prepare("DELETE FROM teacher_schedules WHERE id = ?");
            $stmt->execute([$schedule_id]);
            
            $message = 'সময়সূচী সফলভাবে মুছে ফেলা হয়েছে!';
            
        } catch (Exception $e) {
            $error = 'সময়সূচী মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$filter_teacher = $_GET['filter_teacher'] ?? '';
$filter_day = $_GET['filter_day'] ?? '';
$filter_class = $_GET['filter_class'] ?? '';

// Build WHERE clause for filtering
$where_conditions = [];
$params = [];

if (!empty($filter_teacher)) {
    $where_conditions[] = "ts.teacher_id = ?";
    $params[] = $filter_teacher;
}

if (!empty($filter_day)) {
    $where_conditions[] = "ts.day_of_week = ?";
    $params[] = $filter_day;
}

if (!empty($filter_class)) {
    $where_conditions[] = "ts.class_id = ?";
    $params[] = $filter_class;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// Get schedules with teacher, subject, and class info
try {
    $sql = "
        SELECT ts.*, 
               CONCAT(t.first_name, ' ', t.last_name) as teacher_name,
               s.subject_name,
               CONCAT(c.class_name, ' - ', c.section) as class_info
        FROM teacher_schedules ts
        LEFT JOIN teachers t ON ts.teacher_id = t.id
        LEFT JOIN subjects s ON ts.subject_id = s.id
        LEFT JOIN classes c ON ts.class_id = c.id
        $where_clause
        ORDER BY ts.day_of_week, ts.start_time
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $schedules = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $schedules = [];
}

// Get teachers for dropdown
try {
    $stmt = $pdo->query("SELECT id, CONCAT(first_name, ' ', last_name) as name FROM teachers ORDER BY first_name");
    $teachers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $teachers = [];
}

// Get subjects for dropdown
try {
    $stmt = $pdo->query("SELECT * FROM subjects ORDER BY subject_name");
    $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $subjects = [];
}

// Get classes for dropdown
try {
    $stmt = $pdo->query("SELECT id, CONCAT(class_name, ' - ', section) as class_info FROM classes ORDER BY class_name");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $classes = [];
}

// Days of week in Bengali
$days_bengali = [
    'sunday' => 'রবিবার',
    'monday' => 'সোমবার',
    'tuesday' => 'মঙ্গলবার',
    'wednesday' => 'বুধবার',
    'thursday' => 'বৃহস্পতিবার',
    'friday' => 'শুক্রবার',
    'saturday' => 'শনিবার'
];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক সময়সূচী - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .schedule-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .schedule-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .schedule-title {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .schedule-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-left: 4px solid #667eea;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .form-grid-full {
            grid-column: 1 / -1;
        }
        
        .filter-section {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .schedule-table th {
            background: #667eea;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }
        
        .schedule-table td {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .schedule-table tr:hover {
            background: #f8f9fa;
        }
        
        .day-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .day-sunday { background: #ffebee; color: #c62828; }
        .day-monday { background: #e8f5e8; color: #2e7d32; }
        .day-tuesday { background: #fff3e0; color: #ef6c00; }
        .day-wednesday { background: #f3e5f5; color: #7b1fa2; }
        .day-thursday { background: #e0f2f1; color: #00695c; }
        .day-friday { background: #e1f5fe; color: #0277bd; }
        .day-saturday { background: #fce4ec; color: #ad1457; }
        
        .time-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .subject-badge {
            background: #f3e5f5;
            color: #7b1fa2;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
        }
        
        .class-badge {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
        }
        
        .room-badge {
            background: #fff3e0;
            color: #ef6c00;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.85rem;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
        }
        
        .delete-btn:hover {
            background: #c82333;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .filter-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">📅 শিক্ষক সময়সূচী</h1>
                <p class="content-subtitle">শিক্ষকদের ক্লাসের সময়সূচী ব্যবস্থাপনা করুন</p>
            </div>
            
            <div class="content-body">
                <div class="schedule-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Add New Schedule Form -->
                    <div class="form-section">
                        <h3 class="section-title">
                            ➕ নতুন সময়সূচী যোগ করুন
                        </h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="add_schedule">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label">শিক্ষক নির্বাচন করুন *</label>
                                    <select name="teacher_id" class="form-control" required>
                                        <option value="">শিক্ষক নির্বাচন করুন</option>
                                        <?php foreach ($teachers as $teacher): ?>
                                            <option value="<?php echo $teacher['id']; ?>">
                                                <?php echo htmlspecialchars($teacher['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">বিষয় নির্বাচন করুন *</label>
                                    <select name="subject_id" class="form-control" required>
                                        <option value="">বিষয় নির্বাচন করুন</option>
                                        <?php foreach ($subjects as $subject): ?>
                                            <option value="<?php echo $subject['id']; ?>">
                                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">ক্লাস নির্বাচন করুন *</label>
                                    <select name="class_id" class="form-control" required>
                                        <option value="">ক্লাস নির্বাচন করুন</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>">
                                                <?php echo htmlspecialchars($class['class_info']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">দিন নির্বাচন করুন *</label>
                                    <select name="day_of_week" class="form-control" required>
                                        <option value="">দিন নির্বাচন করুন</option>
                                        <?php foreach ($days_bengali as $day_en => $day_bn): ?>
                                            <option value="<?php echo $day_en; ?>">
                                                <?php echo $day_bn; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">শুরুর সময় *</label>
                                    <input type="time" name="start_time" class="form-control" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">শেষের সময় *</label>
                                    <input type="time" name="end_time" class="form-control" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">রুম নম্বর</label>
                                    <input type="text" name="room_number" class="form-control" 
                                           placeholder="যেমন: ১০১, A-২০৫">
                                </div>
                            </div>
                            
                            <div style="text-align: center; margin-top: 2rem;">
                                <button type="submit" class="submit-btn">
                                    ✅ সময়সূচী যোগ করুন
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <h3 class="section-title">
                            🔍 সময়সূচী ফিল্টার করুন
                        </h3>
                        <form method="GET">
                            <div class="filter-grid">
                                <div class="form-group">
                                    <label class="form-label">শিক্ষক</label>
                                    <select name="filter_teacher" class="form-control">
                                        <option value="">সব শিক্ষক</option>
                                        <?php foreach ($teachers as $teacher): ?>
                                            <option value="<?php echo $teacher['id']; ?>"
                                                    <?php echo ($filter_teacher == $teacher['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($teacher['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">দিন</label>
                                    <select name="filter_day" class="form-control">
                                        <option value="">সব দিন</option>
                                        <?php foreach ($days_bengali as $day_en => $day_bn): ?>
                                            <option value="<?php echo $day_en; ?>"
                                                    <?php echo ($filter_day == $day_en) ? 'selected' : ''; ?>>
                                                <?php echo $day_bn; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">ক্লাস</label>
                                    <select name="filter_class" class="form-control">
                                        <option value="">সব ক্লাস</option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>"
                                                    <?php echo ($filter_class == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_info']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div style="text-align: center; margin-top: 1rem;">
                                <button type="submit" class="filter-btn">🔍 ফিল্টার করুন</button>
                                <a href="?" class="clear-btn" style="text-decoration: none; margin-left: 1rem;">🔄 সব দেখুন</a>
                            </div>
                        </form>
                    </div>

                    <!-- Schedules Table -->
                    <div class="form-section">
                        <h3 class="section-title">
                            📋 বর্তমান সময়সূচী (<?php echo count($schedules); ?> টি)
                        </h3>

                        <?php if (empty($schedules)): ?>
                            <div style="text-align: center; padding: 3rem; color: #6c757d;">
                                <h4>📅 কোন সময়সূচী পাওয়া যায়নি</h4>
                                <p>নতুন সময়সূচী যোগ করুন বা ফিল্টার পরিবর্তন করুন।</p>
                            </div>
                        <?php else: ?>
                            <div style="overflow-x: auto;">
                                <table class="schedule-table">
                                    <thead>
                                        <tr>
                                            <th>দিন</th>
                                            <th>সময়</th>
                                            <th>শিক্ষক</th>
                                            <th>বিষয়</th>
                                            <th>ক্লাস</th>
                                            <th>রুম</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($schedules as $schedule): ?>
                                            <tr>
                                                <td>
                                                    <span class="day-badge day-<?php echo $schedule['day_of_week']; ?>">
                                                        <?php echo $days_bengali[$schedule['day_of_week']]; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="time-badge">
                                                        <?php echo date('g:i A', strtotime($schedule['start_time'])); ?>
                                                    </span>
                                                    -
                                                    <span class="time-badge">
                                                        <?php echo date('g:i A', strtotime($schedule['end_time'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($schedule['teacher_name']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="subject-badge">
                                                        <?php echo htmlspecialchars($schedule['subject_name']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="class-badge">
                                                        <?php echo htmlspecialchars($schedule['class_info']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($schedule['room_number']): ?>
                                                        <span class="room-badge">
                                                            🏫 <?php echo htmlspecialchars($schedule['room_number']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span style="color: #6c757d;">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;"
                                                          onsubmit="return confirm('এই সময়সূচী মুছে ফেলতে চান?')">
                                                        <input type="hidden" name="action" value="delete_schedule">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <button type="submit" class="delete-btn">
                                                            🗑️ মুছুন
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-submit filter form on change
        document.addEventListener('DOMContentLoaded', function() {
            const filterSelects = document.querySelectorAll('.filter-section select');
            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    this.form.submit();
                });
            });
        });

        // Time validation
        document.querySelector('input[name="start_time"]').addEventListener('change', function() {
            const startTime = this.value;
            const endTimeInput = document.querySelector('input[name="end_time"]');

            if (startTime && endTimeInput.value && startTime >= endTimeInput.value) {
                alert('শুরুর সময় শেষের সময়ের আগে হতে হবে।');
                this.value = '';
            }
        });

        document.querySelector('input[name="end_time"]').addEventListener('change', function() {
            const endTime = this.value;
            const startTimeInput = document.querySelector('input[name="start_time"]');

            if (endTime && startTimeInput.value && endTime <= startTimeInput.value) {
                alert('শেষের সময় শুরুর সময়ের পরে হতে হবে।');
                this.value = '';
            }
        });
    </script>
</body>
</html>
