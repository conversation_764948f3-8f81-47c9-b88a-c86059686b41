<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Subjects Table Setup</h2>";
    
    // Create subjects table
    $sql = "CREATE TABLE IF NOT EXISTS subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject_name VARCHAR(100) NOT NULL,
        subject_code VARCHAR(20) NOT NULL UNIQUE,
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Subjects table created successfully!</p>";
    
    // Check if subjects already exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM subjects");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "<p>Adding sample subjects...</p>";
        
        // Insert sample subjects
        $sample_subjects = [
            ['বাংলা', 'BAN101', 'বাংলা ভাষা ও সাহিত্য'],
            ['ইংরেজি', 'ENG101', 'ইংরেজি ভাষা ও সাহিত্য'],
            ['গণিত', 'MAT101', 'গণিত ও সংখ্যাতত্ত্ব'],
            ['বিজ্ঞান', 'SCI101', 'সাধারণ বিজ্ঞান'],
            ['সমাজবিজ্ঞান', 'SOC101', 'সমাজবিজ্ঞান ও ইতিহাস'],
            ['ধর্ম', 'REL101', 'ধর্মীয় শিক্ষা'],
            ['শারীরিক শিক্ষা', 'PHY101', 'শারীরিক শিক্ষা ও স্বাস্থ্য'],
            ['চারু ও কারুকলা', 'ART101', 'চারু ও কারুকলা'],
            ['সংগীত', 'MUS101', 'সংগীত ও নৃত্যকলা'],
            ['কম্পিউটার', 'COM101', 'কম্পিউটার বিজ্ঞান ও তথ্যপ্রযুক্তি'],
            ['পদার্থবিজ্ঞান', 'PHY201', 'পদার্থবিজ্ঞান (উচ্চ মাধ্যমিক)'],
            ['রসায়ন', 'CHE201', 'রসায়ন (উচ্চ মাধ্যমিক)'],
            ['জীববিজ্ঞান', 'BIO201', 'জীববিজ্ঞান (উচ্চ মাধ্যমিক)'],
            ['ভূগোল', 'GEO101', 'ভূগোল ও পরিবেশ'],
            ['অর্থনীতি', 'ECO201', 'অর্থনীতি (উচ্চ মাধ্যমিক)']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO subjects (subject_name, subject_code, description) VALUES (?, ?, ?)");
        
        foreach ($sample_subjects as $subject) {
            $stmt->execute($subject);
        }
        
        echo "<p style='color: green;'>✅ Sample subjects inserted!</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Subjects table already has {$count} subjects.</p>";
    }
    
    // Show current subjects
    $stmt = $pdo->query("SELECT * FROM subjects ORDER BY subject_name");
    $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Subjects:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>Subject Name</th>";
    echo "<th style='padding: 10px;'>Subject Code</th>";
    echo "<th style='padding: 10px;'>Description</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "</tr>";
    
    foreach ($subjects as $subject) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $subject['id'] . "</td>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($subject['subject_name']) . "</td>";
        echo "<td style='padding: 8px; background: #e3f2fd; color: #1976d2;'>" . htmlspecialchars($subject['subject_code']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($subject['description']) . "</td>";
        echo "<td style='padding: 8px;'>";
        if ($subject['status'] == 'active') {
            echo "<span style='background: #d4edda; color: #155724; padding: 4px 8px; border-radius: 4px;'>সক্রিয়</span>";
        } else {
            echo "<span style='background: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 4px;'>নিষ্ক্রিয়</span>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Create teacher_schedule table if not exists
    echo "<br><h3>Creating Teacher Schedule Table:</h3>";
    
    $sql = "CREATE TABLE IF NOT EXISTS teacher_schedule (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT NOT NULL,
        class_id INT NOT NULL,
        subject_id INT NOT NULL,
        day_of_week ENUM('Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday') NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        room_number VARCHAR(20),
        academic_year VARCHAR(10) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_schedule (teacher_id, day_of_week, start_time, end_time, academic_year)
    )";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Teacher schedule table created successfully!</p>";
    
    echo "<br><h3>Database Setup Complete!</h3>";
    echo "<p>✅ All tables are ready for class and subject management.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px;">
    <a href="classes.php" style="display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin-right: 10px;">
        🏫 ক্লাস ব্যবস্থাপনা
    </a>
    <a href="subjects.php" style="display: inline-block; padding: 15px 30px; background: #17a2b8; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        📚 বিষয় ব্যবস্থাপনা
    </a>
</div>
