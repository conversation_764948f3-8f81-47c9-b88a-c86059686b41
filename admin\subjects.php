<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_subject') {
        try {
            $subject_name = trim($_POST['subject_name']);
            $subject_code = trim($_POST['subject_code']);
            $description = trim($_POST['description']);
            $status = $_POST['status'] ?? 'active';
            
            // Validate required fields
            if (empty($subject_name) || empty($subject_code)) {
                throw new Exception('বিষয়ের নাম এবং কোড আবশ্যক।');
            }
            
            // Check if subject code already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM subjects WHERE subject_code = ?");
            $stmt->execute([$subject_code]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('এই বিষয় কোড ইতিমধ্যে ব্যবহৃত হয়েছে।');
            }
            
            // Insert new subject
            $stmt = $pdo->prepare("
                INSERT INTO subjects (subject_name, subject_code, description, status)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$subject_name, $subject_code, $description, $status]);
            
            $message = 'নতুন বিষয় সফলভাবে যোগ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'update_subject') {
        try {
            $id = (int)$_POST['id'];
            $subject_name = trim($_POST['subject_name']);
            $subject_code = trim($_POST['subject_code']);
            $description = trim($_POST['description']);
            $status = $_POST['status'];
            
            // Validate required fields
            if (empty($subject_name) || empty($subject_code)) {
                throw new Exception('বিষয়ের নাম এবং কোড আবশ্যক।');
            }
            
            // Check if subject code already exists (excluding current)
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM subjects WHERE subject_code = ? AND id != ?");
            $stmt->execute([$subject_code, $id]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('এই বিষয় কোড ইতিমধ্যে ব্যবহৃত হয়েছে।');
            }
            
            // Update subject
            $stmt = $pdo->prepare("
                UPDATE subjects 
                SET subject_name = ?, subject_code = ?, description = ?, status = ?
                WHERE id = ?
            ");
            $stmt->execute([$subject_name, $subject_code, $description, $status, $id]);
            
            $message = 'বিষয়ের তথ্য সফলভাবে আপডেট করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'delete_subject') {
        try {
            $id = (int)$_POST['id'];
            
            // Check if subject is used in any schedule or exam
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM teacher_schedule WHERE subject_id = ?");
            $stmt->execute([$id]);
            $schedule_count = $stmt->fetchColumn();
            
            if ($schedule_count > 0) {
                throw new Exception("এই বিষয়টি {$schedule_count}টি ক্লাস রুটিনে ব্যবহৃত হয়েছে। প্রথমে রুটিন থেকে সরান।");
            }
            
            // Delete subject
            $stmt = $pdo->prepare("DELETE FROM subjects WHERE id = ?");
            $stmt->execute([$id]);
            
            $message = 'বিষয় সফলভাবে মুছে ফেলা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get search parameters
$search_name = $_GET['search_name'] ?? '';
$search_code = $_GET['search_code'] ?? '';
$search_status = $_GET['search_status'] ?? '';

// Build search query
$where_conditions = [];
$params = [];

if (!empty($search_name)) {
    $where_conditions[] = "subject_name LIKE ?";
    $params[] = "%{$search_name}%";
}

if (!empty($search_code)) {
    $where_conditions[] = "subject_code LIKE ?";
    $params[] = "%{$search_code}%";
}

if (!empty($search_status)) {
    $where_conditions[] = "status = ?";
    $params[] = $search_status;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get subjects
try {
    $stmt = $pdo->prepare("
        SELECT s.*,
               COUNT(ts.id) as schedule_count
        FROM subjects s
        LEFT JOIN teacher_schedule ts ON s.id = ts.subject_id
        {$where_clause}
        GROUP BY s.id
        ORDER BY s.subject_name
    ");
    $stmt->execute($params);
    $subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $subjects = [];
    $error = 'ডেটা লোড করতে সমস্যা হয়েছে।';
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .subjects-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .search-section {
            background: #fff3cd;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .add-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .add-btn:hover {
            transform: translateY(-2px);
        }
        
        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .subject-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .subject-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .subject-name {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .subject-code {
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .subject-description {
            color: #6c757d;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .subject-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .subject-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn-edit {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            flex: 1;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            flex: 1;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .modal-title {
            color: #2c3e50;
            font-size: 1.5rem;
            margin: 0;
        }
        
        .close {
            color: #aaa;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            border: none;
            background: none;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .subjects-grid {
                grid-template-columns: 1fr;
            }
            
            .search-grid {
                grid-template-columns: 1fr;
            }
            
            .subject-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">📚 বিষয় ব্যবস্থাপনা</h1>
                <p class="content-subtitle">বিষয় তৈরি, সম্পাদনা ও ব্যবস্থাপনা</p>
            </div>

            <div class="content-body">
                <div class="subjects-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count($subjects); ?></div>
                            <div class="stat-label">মোট বিষয়</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count(array_filter($subjects, fn($s) => $s['status'] == 'active')); ?></div>
                            <div class="stat-label">সক্রিয় বিষয়</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo array_sum(array_column($subjects, 'schedule_count')); ?></div>
                            <div class="stat-label">মোট ক্লাস</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo count(array_filter($subjects, fn($s) => $s['schedule_count'] > 0)); ?></div>
                            <div class="stat-label">ব্যবহৃত বিষয়</div>
                        </div>
                    </div>

                    <!-- Add New Subject Button -->
                    <button class="add-btn" onclick="openAddModal()">
                        ➕ নতুন বিষয় যোগ করুন
                    </button>

                    <!-- Search Section -->
                    <div class="search-section">
                        <h3 style="color: #856404; margin-bottom: 1rem;">🔍 বিষয় অনুসন্ধান</h3>

                        <form method="GET" class="search-grid">
                            <div class="form-group">
                                <label class="form-label">বিষয়ের নাম</label>
                                <input type="text" name="search_name" class="form-control"
                                       value="<?php echo htmlspecialchars($search_name); ?>"
                                       placeholder="বিষয়ের নাম লিখুন">
                            </div>

                            <div class="form-group">
                                <label class="form-label">বিষয় কোড</label>
                                <input type="text" name="search_code" class="form-control"
                                       value="<?php echo htmlspecialchars($search_code); ?>"
                                       placeholder="বিষয় কোড লিখুন">
                            </div>

                            <div class="form-group">
                                <label class="form-label">অবস্থা</label>
                                <select name="search_status" class="form-control">
                                    <option value="">সব অবস্থা</option>
                                    <option value="active" <?php echo ($search_status == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                    <option value="inactive" <?php echo ($search_status == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="submit-btn" style="margin-top: 0; padding: 0.75rem 1.5rem;">
                                    🔍 অনুসন্ধান
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Subjects Grid -->
                    <?php if (empty($subjects)): ?>
                        <div style="text-align: center; padding: 3rem; color: #6c757d;">
                            <h3>📚 কোন বিষয় পাওয়া যায়নি</h3>
                            <p>নতুন বিষয় যোগ করুন অথবা অনুসন্ধান ফিল্টার পরিবর্তন করুন।</p>
                        </div>
                    <?php else: ?>
                        <div class="subjects-grid">
                            <?php foreach ($subjects as $subject): ?>
                                <div class="subject-card">
                                    <div class="subject-header">
                                        <h3 class="subject-name"><?php echo htmlspecialchars($subject['subject_name'] ?? ''); ?></h3>
                                        <span class="subject-code"><?php echo htmlspecialchars($subject['subject_code'] ?? ''); ?></span>
                                    </div>

                                    <?php if (!empty($subject['description'])): ?>
                                        <div class="subject-description">
                                            <?php echo htmlspecialchars($subject['description']); ?>
                                        </div>
                                    <?php endif; ?>

                                    <div class="subject-stats">
                                        <div class="stat-item">
                                            <span>📅</span>
                                            <span><?php echo $subject['schedule_count'] ?? 0; ?> ক্লাস</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="status-badge status-<?php echo $subject['status'] ?? 'active'; ?>">
                                                <?php echo ($subject['status'] ?? 'active') == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="subject-actions">
                                        <button class="btn-edit" onclick="openEditModal(<?php echo htmlspecialchars(json_encode($subject)); ?>)">
                                            ✏️ সম্পাদনা
                                        </button>
                                        <button class="btn-delete" onclick="deleteSubject(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['subject_name']); ?>')">
                                            🗑️ মুছুন
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Subject Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">➕ নতুন বিষয় যোগ করুন</h2>
                <button class="close" onclick="closeModal('addModal')">&times;</button>
            </div>

            <form method="POST" id="addForm">
                <input type="hidden" name="action" value="add_subject">

                <div class="form-group">
                    <label class="form-label">বিষয়ের নাম *</label>
                    <input type="text" name="subject_name" class="form-control" required
                           placeholder="যেমন: বাংলা, ইংরেজি, গণিত">
                </div>

                <div class="form-group">
                    <label class="form-label">বিষয় কোড *</label>
                    <input type="text" name="subject_code" class="form-control" required
                           placeholder="যেমন: BAN101, ENG101, MAT101">
                </div>

                <div class="form-group">
                    <label class="form-label">বিবরণ</label>
                    <textarea name="description" class="form-control" rows="3"
                              placeholder="বিষয় সম্পর্কে সংক্ষিপ্ত বিবরণ লিখুন"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">অবস্থা</label>
                    <select name="status" class="form-control">
                        <option value="active">সক্রিয়</option>
                        <option value="inactive">নিষ্ক্রিয়</option>
                    </select>
                </div>

                <button type="submit" class="submit-btn">
                    💾 বিষয় সংরক্ষণ করুন
                </button>
            </form>
        </div>
    </div>

    <!-- Edit Subject Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">✏️ বিষয় সম্পাদনা করুন</h2>
                <button class="close" onclick="closeModal('editModal')">&times;</button>
            </div>

            <form method="POST" id="editForm">
                <input type="hidden" name="action" value="update_subject">
                <input type="hidden" name="id" id="edit_id">

                <div class="form-group">
                    <label class="form-label">বিষয়ের নাম *</label>
                    <input type="text" name="subject_name" id="edit_subject_name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label class="form-label">বিষয় কোড *</label>
                    <input type="text" name="subject_code" id="edit_subject_code" class="form-control" required>
                </div>

                <div class="form-group">
                    <label class="form-label">বিবরণ</label>
                    <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">অবস্থা</label>
                    <select name="status" id="edit_status" class="form-control">
                        <option value="active">সক্রিয়</option>
                        <option value="inactive">নিষ্ক্রিয়</option>
                    </select>
                </div>

                <button type="submit" class="submit-btn">
                    💾 পরিবর্তন সংরক্ষণ করুন
                </button>
            </form>
        </div>
    </div>

    <!-- Delete Form -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_subject">
        <input type="hidden" name="id" id="delete_id">
    </form>

    <script>
        // Modal functions
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        function openEditModal(subjectData) {
            document.getElementById('edit_id').value = subjectData.id || '';
            document.getElementById('edit_subject_name').value = subjectData.subject_name || '';
            document.getElementById('edit_subject_code').value = subjectData.subject_code || '';
            document.getElementById('edit_description').value = subjectData.description || '';
            document.getElementById('edit_status').value = subjectData.status || 'active';

            document.getElementById('editModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function deleteSubject(id, subjectName) {
            if (confirm(`আপনি কি নিশ্চিত যে "${subjectName}" বিষয়টি মুছে ফেলতে চান?\n\nসতর্কতা: এই ক্রিয়াটি পূর্বাবস্থায় ফেরানো যাবে না।`)) {
                document.getElementById('delete_id').value = id;
                document.getElementById('deleteForm').submit();
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // Form validation
        document.getElementById('addForm').addEventListener('submit', function(e) {
            const subjectName = this.querySelector('[name="subject_name"]').value.trim();
            const subjectCode = this.querySelector('[name="subject_code"]').value.trim();

            if (!subjectName || !subjectCode) {
                e.preventDefault();
                alert('বিষয়ের নাম এবং কোড আবশ্যক।');
                return false;
            }
        });

        document.getElementById('editForm').addEventListener('submit', function(e) {
            const subjectName = this.querySelector('[name="subject_name"]').value.trim();
            const subjectCode = this.querySelector('[name="subject_code"]').value.trim();

            if (!subjectName || !subjectCode) {
                e.preventDefault();
                alert('বিষয়ের নাম এবং কোড আবশ্যক।');
                return false;
            }
        });

        // Auto-focus on modal open
        document.addEventListener('DOMContentLoaded', function() {
            const addModal = document.getElementById('addModal');
            const editModal = document.getElementById('editModal');

            // Focus first input when modal opens
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const modal = mutation.target;
                        if (modal.style.display === 'block') {
                            const firstInput = modal.querySelector('input[type="text"]');
                            if (firstInput) {
                                setTimeout(() => firstInput.focus(), 100);
                            }
                        }
                    }
                });
            });

            observer.observe(addModal, { attributes: true });
            observer.observe(editModal, { attributes: true });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        openAddModal();
                        break;
                }
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal[style*="block"]');
                if (openModal) {
                    openModal.style.display = 'none';
                }
            }
        });

        // Auto-generate subject code
        document.querySelector('[name="subject_name"]').addEventListener('input', function() {
            const subjectName = this.value.trim();
            const codeField = document.querySelector('[name="subject_code"]');

            if (subjectName && !codeField.value) {
                // Generate code from first 3 letters + 101
                const code = subjectName.substring(0, 3).toUpperCase() + '101';
                codeField.value = code;
            }
        });
    </script>
</body>
</html>
