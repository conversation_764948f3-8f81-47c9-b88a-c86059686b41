<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'mark_attendance') {
        try {
            $class_id = (int)$_POST['class_id'];
            $attendance_date = $_POST['attendance_date'];
            $attendance_data = $_POST['attendance'] ?? [];
            
            if (empty($class_id) || empty($attendance_date)) {
                throw new Exception('ক্লাস এবং তারিখ নির্বাচন করুন।');
            }
            
            // Check if attendance already exists for this date and class
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM student_attendance WHERE class_id = ? AND attendance_date = ?");
            $stmt->execute([$class_id, $attendance_date]);
            $exists = $stmt->fetchColumn() > 0;
            
            if ($exists) {
                // Update existing attendance
                $stmt = $pdo->prepare("DELETE FROM student_attendance WHERE class_id = ? AND attendance_date = ?");
                $stmt->execute([$class_id, $attendance_date]);
            }
            
            // Insert new attendance records
            $stmt = $pdo->prepare("
                INSERT INTO student_attendance (student_id, class_id, attendance_date, status, marked_by)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($attendance_data as $student_id => $status) {
                $stmt->execute([$student_id, $class_id, $attendance_date, $status, $_SESSION['user_id']]);
            }
            
            $message = 'উপস্থিতি সফলভাবে সংরক্ষণ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get filter parameters
$selected_class = $_GET['class_id'] ?? '';
$selected_date = $_GET['attendance_date'] ?? date('Y-m-d');

// Get classes for dropdown
try {
    $stmt = $pdo->query("SELECT id, class_name, section FROM classes ORDER BY class_name");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $classes = [];
}

// Get students for selected class
$students = [];
$existing_attendance = [];

if (!empty($selected_class)) {
    try {
        // Get students in the selected class
        $stmt = $pdo->prepare("SELECT s.*, c.class_name, c.section FROM students s JOIN classes c ON s.class_id = c.id WHERE s.class_id = ? ORDER BY s.first_name, s.last_name");
        $stmt->execute([$selected_class]);
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get existing attendance for the selected date and class
        $stmt = $pdo->prepare("
            SELECT sa.student_id, sa.status
            FROM student_attendance sa
            WHERE sa.class_id = ? AND sa.attendance_date = ?
        ");
        $stmt->execute([$selected_class, $selected_date]);
        $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($attendance_records as $record) {
            $existing_attendance[$record['student_id']] = $record['status'];
        }

    } catch (PDOException $e) {
        $students = [];
    }
}

// Get attendance statistics
$attendance_stats = [];
if (!empty($selected_class)) {
    try {
        $stmt = $pdo->prepare("
            SELECT
                COUNT(CASE WHEN sa.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN sa.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN sa.status = 'late' THEN 1 END) as late_count,
                COUNT(*) as total_records
            FROM student_attendance sa
            WHERE sa.class_id = ? AND sa.attendance_date = ?
        ");
        $stmt->execute([$selected_class, $selected_date]);
        $attendance_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $attendance_stats = ['present_count' => 0, 'absent_count' => 0, 'late_count' => 0, 'total_records' => 0];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র উপস্থিতি - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .attendance-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .section-title {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .filter-section {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .stats-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .present { color: #28a745; }
        .absent { color: #dc3545; }
        .late { color: #ffc107; }
        .total { color: #007bff; }
        
        .attendance-form {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .student-row {
            display: grid;
            grid-template-columns: 60px 1fr 200px;
            gap: 1rem;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .student-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .student-info {
            display: flex;
            flex-direction: column;
        }
        
        .student-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }
        
        .student-id {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .attendance-options {
            display: flex;
            gap: 0.5rem;
        }
        
        .attendance-btn {
            padding: 0.5rem 1rem;
            border: 2px solid;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            background: white;
        }
        
        .attendance-btn.present {
            border-color: #28a745;
            color: #28a745;
        }
        
        .attendance-btn.present.active {
            background: #28a745;
            color: white;
        }
        
        .attendance-btn.absent {
            border-color: #dc3545;
            color: #dc3545;
        }
        
        .attendance-btn.absent.active {
            background: #dc3545;
            color: white;
        }
        
        .attendance-btn.late {
            border-color: #ffc107;
            color: #ffc107;
        }
        
        .attendance-btn.late.active {
            background: #ffc107;
            color: #212529;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
            margin-top: 2rem;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
        }
        
        .submit-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .quick-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .quick-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .quick-btn.all-present {
            background: #28a745;
            color: white;
        }
        
        .quick-btn.all-absent {
            background: #dc3545;
            color: white;
        }
        
        .quick-btn.clear-all {
            background: #6c757d;
            color: white;
        }
        
        @media (max-width: 768px) {
            .student-row {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 0.5rem;
            }
            
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">📋 ছাত্র উপস্থিতি</h1>
                <p class="content-subtitle">দৈনিক উপস্থিতি নিবন্ধন ও ব্যবস্থাপনা</p>
            </div>

            <div class="content-body">
                <div class="attendance-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">🔍 ক্লাস ও তারিখ নির্বাচন</h3>

                        <form method="GET" class="filter-grid">
                            <div class="form-group">
                                <label class="form-label">ক্লাস নির্বাচন করুন</label>
                                <select name="class_id" class="form-control" onchange="this.form.submit()">
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php foreach ($classes as $class): ?>
                                        <option value="<?php echo htmlspecialchars($class['id']); ?>"
                                                <?php echo ($selected_class == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">তারিখ নির্বাচন করুন</label>
                                <input type="date" name="attendance_date" class="form-control"
                                       value="<?php echo htmlspecialchars($selected_date); ?>"
                                       onchange="this.form.submit()">
                            </div>

                            <div class="form-group">
                                <button type="submit" class="submit-btn" style="margin-top: 0; padding: 0.75rem 1.5rem;">
                                    🔄 আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>

                    <?php if (!empty($selected_class)): ?>
                        <!-- Statistics Section -->
                        <div class="stats-section">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem;">📊 উপস্থিতির পরিসংখ্যান</h3>

                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number present"><?php echo $attendance_stats['present_count'] ?? 0; ?></div>
                                    <div class="stat-label">উপস্থিত</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number absent"><?php echo $attendance_stats['absent_count'] ?? 0; ?></div>
                                    <div class="stat-label">অনুপস্থিত</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number late"><?php echo $attendance_stats['late_count'] ?? 0; ?></div>
                                    <div class="stat-label">দেরিতে আসা</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number total"><?php echo count($students); ?></div>
                                    <div class="stat-label">মোট ছাত্র</div>
                                </div>
                            </div>
                        </div>

                        <!-- Attendance Form -->
                        <div class="attendance-form">
                            <h3 style="color: #2c3e50; margin-bottom: 1rem;">
                                ✏️ উপস্থিতি চিহ্নিত করুন -
                                <?php
                                $class_info = '';
                                foreach ($classes as $class) {
                                    if ($class['id'] == $selected_class) {
                                        $class_info = $class['class_name'] . ' - ' . $class['section'];
                                        break;
                                    }
                                }
                                echo htmlspecialchars($class_info);
                                ?>
                                <span style="color: #6c757d; font-size: 1rem;">(<?php echo date('d/m/Y', strtotime($selected_date)); ?>)</span>
                            </h3>

                            <?php if (empty($students)): ?>
                                <div style="text-align: center; padding: 3rem; color: #6c757d;">
                                    <h4>👥 এই ক্লাসে কোন ছাত্র নেই</h4>
                                    <p>প্রথমে ছাত্র যোগ করুন অথবা অন্য ক্লাস নির্বাচন করুন।</p>
                                </div>
                            <?php else: ?>
                                <form method="POST" id="attendanceForm">
                                    <input type="hidden" name="action" value="mark_attendance">
                                    <input type="hidden" name="class_id" value="<?php echo htmlspecialchars($selected_class); ?>">
                                    <input type="hidden" name="attendance_date" value="<?php echo htmlspecialchars($selected_date); ?>">

                                    <!-- Quick Actions -->
                                    <div class="quick-actions">
                                        <button type="button" class="quick-btn all-present" onclick="markAll('present')">
                                            ✅ সবাইকে উপস্থিত
                                        </button>
                                        <button type="button" class="quick-btn all-absent" onclick="markAll('absent')">
                                            ❌ সবাইকে অনুপস্থিত
                                        </button>
                                        <button type="button" class="quick-btn clear-all" onclick="clearAll()">
                                            🔄 সব ক্লিয়ার
                                        </button>
                                    </div>

                                    <!-- Student List -->
                                    <?php foreach ($students as $student): ?>
                                        <div class="student-row">
                                            <div>
                                                <?php if (isset($student['photo']) && $student['photo']): ?>
                                                    <img src="../<?php echo htmlspecialchars($student['photo']); ?>"
                                                         alt="ছবি" class="student-photo">
                                                <?php else: ?>
                                                    <div style="width: 50px; height: 50px; background: #dee2e6; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                        👤
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="student-info">
                                                <div class="student-name">
                                                    <?php echo htmlspecialchars(($student['first_name'] ?? '') . ' ' . ($student['last_name'] ?? '')); ?>
                                                </div>
                                                <div class="student-id">
                                                    ID: <?php echo htmlspecialchars($student['student_id'] ?? ''); ?>
                                                </div>
                                            </div>

                                            <div class="attendance-options">
                                                <?php $current_status = $existing_attendance[$student['id']] ?? ''; ?>

                                                <label class="attendance-btn present <?php echo ($current_status == 'present') ? 'active' : ''; ?>">
                                                    <input type="radio" name="attendance[<?php echo $student['id']; ?>]"
                                                           value="present" style="display: none;"
                                                           <?php echo ($current_status == 'present') ? 'checked' : ''; ?>>
                                                    ✅ উপস্থিত
                                                </label>

                                                <label class="attendance-btn absent <?php echo ($current_status == 'absent') ? 'active' : ''; ?>">
                                                    <input type="radio" name="attendance[<?php echo $student['id']; ?>]"
                                                           value="absent" style="display: none;"
                                                           <?php echo ($current_status == 'absent') ? 'checked' : ''; ?>>
                                                    ❌ অনুপস্থিত
                                                </label>

                                                <label class="attendance-btn late <?php echo ($current_status == 'late') ? 'active' : ''; ?>">
                                                    <input type="radio" name="attendance[<?php echo $student['id']; ?>]"
                                                           value="late" style="display: none;"
                                                           <?php echo ($current_status == 'late') ? 'checked' : ''; ?>>
                                                    ⏰ দেরি
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>

                                    <button type="submit" class="submit-btn">
                                        💾 উপস্থিতি সংরক্ষণ করুন
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem; color: #6c757d;">
                            <h4>📚 ক্লাস নির্বাচন করুন</h4>
                            <p>উপস্থিতি চিহ্নিত করতে প্রথমে একটি ক্লাস নির্বাচন করুন।</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Handle attendance button clicks
        document.addEventListener('DOMContentLoaded', function() {
            const attendanceButtons = document.querySelectorAll('.attendance-btn');

            attendanceButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const radio = this.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;

                        // Remove active class from siblings
                        const siblings = this.parentElement.querySelectorAll('.attendance-btn');
                        siblings.forEach(sibling => sibling.classList.remove('active'));

                        // Add active class to clicked button
                        this.classList.add('active');

                        // Update statistics
                        updateStatistics();
                    }
                });
            });
        });

        // Quick action functions
        function markAll(status) {
            const radios = document.querySelectorAll(`input[value="${status}"]`);
            const buttons = document.querySelectorAll(`.attendance-btn.${status}`);

            // Clear all active states first
            document.querySelectorAll('.attendance-btn').forEach(btn => btn.classList.remove('active'));

            // Set all radios and buttons for the specified status
            radios.forEach(radio => {
                radio.checked = true;
            });

            buttons.forEach(button => {
                button.classList.add('active');
            });

            updateStatistics();
        }

        function clearAll() {
            // Clear all radio buttons
            document.querySelectorAll('input[type="radio"]').forEach(radio => {
                radio.checked = false;
            });

            // Remove all active classes
            document.querySelectorAll('.attendance-btn').forEach(button => {
                button.classList.remove('active');
            });

            updateStatistics();
        }

        // Update statistics in real-time
        function updateStatistics() {
            const presentCount = document.querySelectorAll('input[value="present"]:checked').length;
            const absentCount = document.querySelectorAll('input[value="absent"]:checked').length;
            const lateCount = document.querySelectorAll('input[value="late"]:checked').length;

            // Update stat cards if they exist
            const statCards = document.querySelectorAll('.stat-number');
            if (statCards.length >= 3) {
                statCards[0].textContent = presentCount;
                statCards[1].textContent = absentCount;
                statCards[2].textContent = lateCount;
            }
        }

        // Form validation
        document.getElementById('attendanceForm')?.addEventListener('submit', function(e) {
            const checkedRadios = document.querySelectorAll('input[type="radio"]:checked');
            const totalStudents = document.querySelectorAll('.student-row').length;

            if (checkedRadios.length === 0) {
                e.preventDefault();
                alert('অন্তত একজন ছাত্রের উপস্থিতি চিহ্নিত করুন।');
                return false;
            }

            if (checkedRadios.length < totalStudents) {
                const confirmed = confirm(`${totalStudents - checkedRadios.length} জন ছাত্রের উপস্থিতি চিহ্নিত করা হয়নি। এগিয়ে যেতে চান?`);
                if (!confirmed) {
                    e.preventDefault();
                    return false;
                }
            }

            // Show loading state
            const submitBtn = this.querySelector('.submit-btn');
            submitBtn.disabled = true;
            submitBtn.textContent = '💾 সংরক্ষণ করা হচ্ছে...';
        });

        // Auto-save functionality (optional)
        let autoSaveTimeout;
        function autoSave() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                const form = document.getElementById('attendanceForm');
                const checkedRadios = form?.querySelectorAll('input[type="radio"]:checked');

                if (checkedRadios && checkedRadios.length > 0) {
                    // Show auto-save indicator
                    const indicator = document.createElement('div');
                    indicator.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 20px; border-radius: 5px; z-index: 1000;';
                    indicator.textContent = '💾 স্বয়ংক্রিয় সংরক্ষণ...';
                    document.body.appendChild(indicator);

                    setTimeout(() => {
                        indicator.remove();
                    }, 2000);
                }
            }, 5000); // Auto-save after 5 seconds of inactivity
        }

        // Add auto-save listeners
        document.addEventListener('DOMContentLoaded', function() {
            const radioButtons = document.querySelectorAll('input[type="radio"]');
            radioButtons.forEach(radio => {
                radio.addEventListener('change', autoSave);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        document.getElementById('attendanceForm')?.submit();
                        break;
                    case 'a':
                        e.preventDefault();
                        markAll('present');
                        break;
                    case 'd':
                        e.preventDefault();
                        markAll('absent');
                        break;
                    case 'r':
                        e.preventDefault();
                        clearAll();
                        break;
                }
            }
        });

        // Show keyboard shortcuts help
        function showKeyboardHelp() {
            alert(`কীবোর্ড শর্টকাট:

Ctrl+S: উপস্থিতি সংরক্ষণ
Ctrl+A: সবাইকে উপস্থিত
Ctrl+D: সবাইকে অনুপস্থিত
Ctrl+R: সব ক্লিয়ার`);
        }

        // Add help button
        document.addEventListener('DOMContentLoaded', function() {
            const helpBtn = document.createElement('button');
            helpBtn.type = 'button';
            helpBtn.className = 'quick-btn';
            helpBtn.style.background = '#17a2b8';
            helpBtn.style.color = 'white';
            helpBtn.textContent = '❓ সাহায্য';
            helpBtn.onclick = showKeyboardHelp;

            const quickActions = document.querySelector('.quick-actions');
            if (quickActions) {
                quickActions.appendChild(helpBtn);
            }
        });
    </script>
</body>
</html>
