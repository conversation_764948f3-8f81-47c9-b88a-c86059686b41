<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

// Get student IDs from query parameter
$student_ids = isset($_GET['ids']) ? explode(',', $_GET['ids']) : [];

if (empty($student_ids)) {
    header('Location: bulk_operations.php');
    exit;
}

// Prepare placeholders for IN clause
$placeholders = str_repeat('?,', count($student_ids) - 1) . '?';

// Get selected students data
try {
    $stmt = $pdo->prepare("
        SELECT 
            s.student_id,
            s.first_name,
            s.last_name,
            c.class_name,
            c.section,
            s.roll_number,
            s.date_of_birth,
            s.gender,
            s.father_name,
            s.mother_name,
            s.guardian_name,
            s.guardian_relation,
            s.guardian_phone,
            s.guardian_email,
            s.guardian_address,
            s.phone,
            s.parent_phone,
            s.address,
            u.username,
            u.email,
            s.admission_date
        FROM students s
        LEFT JOIN classes c ON s.class_id = c.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.id IN ($placeholders)
        ORDER BY s.first_name, s.last_name
    ");
    
    $stmt->execute($student_ids);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    header('Location: bulk_operations.php?error=export_failed');
    exit;
}

if (empty($students)) {
    header('Location: bulk_operations.php?error=no_students');
    exit;
}

// Set headers for CSV download
$filename = 'students_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Bengali characters in Excel)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Header
$header = [
    'ছাত্র ID',
    'নাম',
    'পদবি',
    'ক্লাস',
    'সেকশন',
    'রোল নম্বর',
    'জন্ম তারিখ',
    'লিঙ্গ',
    'পিতার নাম',
    'মাতার নাম',
    'অভিভাবকের নাম',
    'অভিভাবকের সম্পর্ক',
    'অভিভাবকের ফোন',
    'অভিভাবকের ইমেইল',
    'অভিভাবকের ঠিকানা',
    'ছাত্রের ফোন',
    'পিতা-মাতার ফোন',
    'ঠিকানা',
    'ইউজারনেম',
    'ইমেইল',
    'ভর্তির তারিখ'
];

// Write header
fputcsv($output, $header);

// Write student data
foreach ($students as $student) {
    $row = [
        $student['student_id'],
        $student['first_name'],
        $student['last_name'],
        $student['class_name'],
        $student['section'],
        $student['roll_number'],
        $student['date_of_birth'],
        $student['gender'] === 'male' ? 'পুরুষ' : ($student['gender'] === 'female' ? 'মহিলা' : ''),
        $student['father_name'],
        $student['mother_name'],
        $student['guardian_name'],
        $student['guardian_relation'],
        $student['guardian_phone'],
        $student['guardian_email'],
        $student['guardian_address'],
        $student['phone'],
        $student['parent_phone'],
        $student['address'],
        $student['username'],
        $student['email'],
        $student['admission_date']
    ];
    
    fputcsv($output, $row);
}

// Close file pointer
fclose($output);
exit;
?>
