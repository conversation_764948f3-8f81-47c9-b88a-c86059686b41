<?php
session_start();
require_once 'config/database.php';

// যদি ইতিমধ্যে লগইন থাকে তাহলে ড্যাশবোর্ডে পাঠানো
if (isLoggedIn()) {
    $user = getCurrentUser();
    switch ($user['role']) {
        case 'admin':
            header('Location: admin/dashboard.php');
            break;
        case 'teacher':
            header('Location: teacher/dashboard.php');
            break;
        case 'student':
            header('Location: student/dashboard.php');
            break;
    }
    exit;
}

$error_message = '';

// লগইন প্রসেসিং
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error_message = 'ইউজারনেম এবং পাসওয়ার্ড দিন';
    } else {
        $db = getDB();
        $query = "SELECT * FROM users WHERE username = ? AND status = 'active'";
        $user = $db->selectOne($query, [$username]);
        
        if ($user && verifyPassword($password, $user['password'])) {
            // সেশন সেট করা
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            
            // রোল অনুযায়ী রিডাইরেক্ট
            switch ($user['role']) {
                case 'admin':
                    header('Location: admin/dashboard.php');
                    break;
                case 'teacher':
                    header('Location: teacher/dashboard.php');
                    break;
                case 'student':
                    header('Location: student/dashboard.php');
                    break;
                default:
                    header('Location: index.php');
            }
            exit;
        } else {
            $error_message = 'ভুল ইউজারনেম বা পাসওয়ার্ড';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
        }
        
        .demo-credentials {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        .demo-credentials h4 {
            margin-bottom: 0.5rem;
            color: #495057;
        }
        
        .demo-credentials p {
            margin: 0.25rem 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>স্কুল ব্যবস্থাপনা সিস্টেম</h1>
                <p>আপনার অ্যাকাউন্টে লগইন করুন</p>
            </div>
            
            <!-- ডেমো ক্রেডেনশিয়াল -->
            <div class="demo-credentials">
                <h4>ডেমো লগইন তথ্য:</h4>
                <p><strong>অ্যাডমিন:</strong> admin / password</p>
                <p><strong>শিক্ষক:</strong> teacher / password</p>
                <p><strong>ছাত্র:</strong> student / password</p>
            </div>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" data-validate>
                <div class="form-group">
                    <label for="username" class="form-label">ইউজারনেম</label>
                    <input type="text" id="username" name="username" class="form-control" 
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                           required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">পাসওয়ার্ড</label>
                    <input type="password" id="password" name="password" class="form-control" required>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%;">লগইন করুন</button>
            </form>
            
            <div class="text-center mt-3">
                <p><a href="forgot-password.php">পাসওয়ার্ড ভুলে গেছেন?</a></p>
            </div>
        </div>
    </div>
    
    <script src="assets/js/main.js"></script>
</body>
</html>
