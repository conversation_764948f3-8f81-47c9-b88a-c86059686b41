<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$db = new Database();
$pdo = $db->getConnection();

$message = '';
$error = '';

// Create fees related tables if they don't exist
try {
    // Fee categories table
    $pdo->exec("CREATE TABLE IF NOT EXISTS fee_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_name VARCHAR(100) NOT NULL,
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    
    // Fee structures table
    $pdo->exec("CREATE TABLE IF NOT EXISTS fee_structures (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        category_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        academic_year VARCHAR(10) NOT NULL,
        due_date DATE,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES fee_categories(id) ON DELETE CASCADE
    )");
    
    // Student fees table
    $pdo->exec("CREATE TABLE IF NOT EXISTS student_fees (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        fee_structure_id INT NOT NULL,
        amount_due DECIMAL(10,2) NOT NULL,
        amount_paid DECIMAL(10,2) DEFAULT 0,
        payment_date DATE NULL,
        payment_method ENUM('cash', 'bank', 'online', 'cheque') NULL,
        receipt_number VARCHAR(50) NULL,
        status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
        remarks TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (fee_structure_id) REFERENCES fee_structures(id) ON DELETE CASCADE
    )");
    
    // Insert default fee categories if none exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM fee_categories");
    if ($stmt->fetchColumn() == 0) {
        $default_categories = [
            ['মাসিক বেতন', 'প্রতি মাসের নিয়মিত বেতন'],
            ['ভর্তি ফি', 'নতুন ছাত্র ভর্তির সময় একবার প্রদেয়'],
            ['পরীক্ষা ফি', 'বার্ষিক ও অর্ধবার্ষিক পরীক্ষার ফি'],
            ['বই ফি', 'পাঠ্যবই ও খাতা-কলমের ফি'],
            ['খেলাধুলা ফি', 'খেলাধুলা ও সাংস্কৃতিক কার্যক্রমের ফি'],
            ['পরিবহন ফি', 'স্কুল বাস ও পরিবহন খরচ'],
            ['উন্নয়ন ফি', 'স্কুল উন্নয়ন ও রক্ষণাবেক্ষণ ফি'],
            ['কম্পিউটার ফি', 'কম্পিউটার ল্যাব ও প্রযুক্তি ফি']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO fee_categories (category_name, description) VALUES (?, ?)");
        foreach ($default_categories as $category) {
            $stmt->execute($category);
        }
    }
    
} catch (PDOException $e) {
    $error = "Database setup error: " . $e->getMessage();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_fee_structure') {
        try {
            $class_id = (int)$_POST['class_id'];
            $category_id = (int)$_POST['category_id'];
            $amount = (float)$_POST['amount'];
            $academic_year = trim($_POST['academic_year']) ?: date('Y');
            $due_date = $_POST['due_date'] ?: null;
            
            if (empty($class_id) || empty($category_id) || $amount <= 0) {
                throw new Exception('সকল তথ্য সঠিকভাবে পূরণ করুন।');
            }
            
            $stmt = $pdo->prepare("INSERT INTO fee_structures (class_id, category_id, amount, academic_year, due_date) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$class_id, $category_id, $amount, $academic_year, $due_date]);
            
            $message = 'ফি স্ট্রাকচার সফলভাবে যোগ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'add_category') {
        try {
            $category_name = trim($_POST['category_name']);
            $description = trim($_POST['description']);
            
            if (empty($category_name)) {
                throw new Exception('ক্যাটেগরির নাম আবশ্যক।');
            }
            
            $stmt = $pdo->prepare("INSERT INTO fee_categories (category_name, description) VALUES (?, ?)");
            $stmt->execute([$category_name, $description]);
            
            $message = 'নতুন ফি ক্যাটেগরি যোগ করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
    
    if ($action === 'collect_fee') {
        try {
            $student_id = (int)$_POST['student_id'];
            $fee_structure_id = (int)$_POST['fee_structure_id'];
            $amount_paid = (float)$_POST['amount_paid'];
            $payment_method = $_POST['payment_method'];
            $receipt_number = trim($_POST['receipt_number']);
            $remarks = trim($_POST['remarks']);
            
            if (empty($student_id) || empty($fee_structure_id) || $amount_paid <= 0) {
                throw new Exception('সকল তথ্য সঠিকভাবে পূরণ করুন।');
            }
            
            // Check if student fee record exists
            $stmt = $pdo->prepare("SELECT * FROM student_fees WHERE student_id = ? AND fee_structure_id = ?");
            $stmt->execute([$student_id, $fee_structure_id]);
            $existing_fee = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing_fee) {
                // Update existing record
                $new_total_paid = $existing_fee['amount_paid'] + $amount_paid;
                $status = ($new_total_paid >= $existing_fee['amount_due']) ? 'paid' : 'partial';
                
                $stmt = $pdo->prepare("UPDATE student_fees SET amount_paid = ?, payment_date = CURDATE(), payment_method = ?, receipt_number = ?, status = ?, remarks = ? WHERE id = ?");
                $stmt->execute([$new_total_paid, $payment_method, $receipt_number, $status, $remarks, $existing_fee['id']]);
            } else {
                // Get fee structure details
                $stmt = $pdo->prepare("SELECT amount FROM fee_structures WHERE id = ?");
                $stmt->execute([$fee_structure_id]);
                $fee_structure = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$fee_structure) {
                    throw new Exception('ফি স্ট্রাকচার পাওয়া যায়নি।');
                }
                
                $amount_due = $fee_structure['amount'];
                $status = ($amount_paid >= $amount_due) ? 'paid' : 'partial';
                
                // Create new record
                $stmt = $pdo->prepare("INSERT INTO student_fees (student_id, fee_structure_id, amount_due, amount_paid, payment_date, payment_method, receipt_number, status, remarks) VALUES (?, ?, ?, ?, CURDATE(), ?, ?, ?, ?)");
                $stmt->execute([$student_id, $fee_structure_id, $amount_due, $amount_paid, $payment_method, $receipt_number, $status, $remarks]);
            }
            
            $message = 'ফি সংগ্রহ সফলভাবে রেকর্ড করা হয়েছে!';
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Get filter parameters
$selected_class = $_GET['class_id'] ?? '';
$selected_year = $_GET['academic_year'] ?? date('Y');
$selected_category = $_GET['category_id'] ?? '';

// Get data
$classes = [];
$categories = [];
$students = [];
$fee_structures = [];
$student_fees = [];

try {
    // Get classes
    $stmt = $pdo->query("SELECT id, class_name, section, academic_year FROM classes WHERE status = 'active' ORDER BY class_name, section");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get categories
    $stmt = $pdo->query("SELECT id, category_name, description FROM fee_categories WHERE status = 'active' ORDER BY category_name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get students for selected class
    if (!empty($selected_class)) {
        $stmt = $pdo->prepare("SELECT id, student_name, student_id, class_id FROM students WHERE class_id = ? AND status = 'active' ORDER BY student_name");
        $stmt->execute([$selected_class]);
        $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get fee structures
    $where_conditions = ["fs.status = 'active'"];
    $params = [];
    
    if (!empty($selected_class)) {
        $where_conditions[] = "fs.class_id = ?";
        $params[] = $selected_class;
    }
    
    if (!empty($selected_year)) {
        $where_conditions[] = "fs.academic_year = ?";
        $params[] = $selected_year;
    }
    
    if (!empty($selected_category)) {
        $where_conditions[] = "fs.category_id = ?";
        $params[] = $selected_category;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $stmt = $pdo->prepare("
        SELECT fs.*, c.class_name, c.section, fc.category_name
        FROM fee_structures fs
        JOIN classes c ON fs.class_id = c.id
        JOIN fee_categories fc ON fs.category_id = fc.id
        WHERE {$where_clause}
        ORDER BY c.class_name, c.section, fc.category_name
    ");
    $stmt->execute($params);
    $fee_structures = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get student fees for selected class
    if (!empty($selected_class)) {
        $stmt = $pdo->prepare("
            SELECT sf.*, s.student_name, s.student_id, fs.amount as total_amount, fc.category_name
            FROM student_fees sf
            JOIN students s ON sf.student_id = s.id
            JOIN fee_structures fs ON sf.fee_structure_id = fs.id
            JOIN fee_categories fc ON fs.category_id = fc.id
            WHERE s.class_id = ? AND fs.academic_year = ?
            ORDER BY s.student_name, fc.category_name
        ");
        $stmt->execute([$selected_class, $selected_year]);
        $student_fees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $error = "Data loading error: " . $e->getMessage();
}

// Calculate statistics
$total_categories = count($categories);
$total_structures = count($fee_structures);
$total_collected = 0;
$total_due = 0;

foreach ($student_fees as $fee) {
    $total_collected += $fee['amount_paid'];
    $total_due += ($fee['amount_due'] - $fee['amount_paid']);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ব্যবস্থাপনা - স্কুল ব্যবস্থাপনা</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .fees-container {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid #e9ecef;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .tabs-container {
            margin-bottom: 2rem;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 2rem;
        }
        
        .tab {
            padding: 1rem 2rem;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #6c757d;
            border-radius: 8px 8px 0 0;
            margin-right: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            background: #e8f5e8;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-partial {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }
        
        .amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .amount-due {
            color: #dc3545;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                margin-right: 0;
                margin-bottom: 0.5rem;
            }
            
            .data-table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">💰 ফি ব্যবস্থাপনা</h1>
                <p class="content-subtitle">ছাত্রদের ফি নির্ধারণ, সংগ্রহ ও রিপোর্ট ব্যবস্থাপনা</p>
            </div>

            <div class="content-body">
                <div class="fees-container">
                    <!-- Messages -->
                    <?php if ($message): ?>
                        <div class="alert alert-success">
                            ✅ <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            ❌ <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Statistics -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $total_categories; ?></div>
                            <div class="stat-label">ফি ক্যাটেগরি</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $total_structures; ?></div>
                            <div class="stat-label">ফি স্ট্রাকচার</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">৳<?php echo number_format($total_collected, 2); ?></div>
                            <div class="stat-label">মোট সংগৃহীত</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">৳<?php echo number_format($total_due, 2); ?></div>
                            <div class="stat-label">মোট বকেয়া</div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <h3 style="color: #2c3e50; margin-bottom: 1rem;">🔍 ফিল্টার অপশন</h3>

                        <form method="GET" class="filter-grid">
                            <div class="form-group">
                                <label class="form-label">ক্লাস নির্বাচন করুন</label>
                                <select name="class_id" class="form-control" onchange="this.form.submit()">
                                    <option value="">সকল ক্লাস</option>
                                    <?php foreach ($classes as $class): ?>
                                        <option value="<?php echo $class['id']; ?>"
                                                <?php echo ($selected_class == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section'] . ' (' . $class['academic_year'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">শিক্ষাবর্ষ</label>
                                <select name="academic_year" class="form-control" onchange="this.form.submit()">
                                    <option value="<?php echo date('Y'); ?>" <?php echo ($selected_year == date('Y')) ? 'selected' : ''; ?>><?php echo date('Y'); ?></option>
                                    <option value="<?php echo (date('Y')-1); ?>" <?php echo ($selected_year == (date('Y')-1)) ? 'selected' : ''; ?>><?php echo (date('Y')-1); ?></option>
                                    <option value="<?php echo (date('Y')+1); ?>" <?php echo ($selected_year == (date('Y')+1)) ? 'selected' : ''; ?>><?php echo (date('Y')+1); ?></option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">ফি ক্যাটেগরি</label>
                                <select name="category_id" class="form-control" onchange="this.form.submit()">
                                    <option value="">সকল ক্যাটেগরি</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"
                                                <?php echo ($selected_category == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>

                    <!-- Tabs -->
                    <div class="tabs-container">
                        <div class="tabs">
                            <button class="tab active" onclick="showTab('structures')">📋 ফি স্ট্রাকচার</button>
                            <button class="tab" onclick="showTab('collection')">💳 ফি সংগ্রহ</button>
                            <button class="tab" onclick="showTab('reports')">📊 রিপোর্ট</button>
                            <button class="tab" onclick="showTab('categories')">🏷️ ক্যাটেগরি</button>
                        </div>

                        <!-- Fee Structures Tab -->
                        <div id="structures" class="tab-content active">
                            <div class="form-section">
                                <h3 style="color: #155724; margin-bottom: 1rem;">➕ নতুন ফি স্ট্রাকচার যোগ করুন</h3>

                                <form method="POST" class="form-grid">
                                    <input type="hidden" name="action" value="add_fee_structure">

                                    <div class="form-group">
                                        <label class="form-label">ক্লাস নির্বাচন করুন *</label>
                                        <select name="class_id" class="form-control" required>
                                            <option value="">ক্লাস নির্বাচন করুন</option>
                                            <?php foreach ($classes as $class): ?>
                                                <option value="<?php echo $class['id']; ?>">
                                                    <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">ফি ক্যাটেগরি *</label>
                                        <select name="category_id" class="form-control" required>
                                            <option value="">ক্যাটেগরি নির্বাচন করুন</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>">
                                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">ফি পরিমাণ (টাকা) *</label>
                                        <input type="number" name="amount" class="form-control" step="0.01" min="0" required placeholder="0.00">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">শিক্ষাবর্ষ</label>
                                        <input type="text" name="academic_year" class="form-control" value="<?php echo date('Y'); ?>" placeholder="2025">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">শেষ তারিখ</label>
                                        <input type="date" name="due_date" class="form-control">
                                    </div>

                                    <div class="form-group" style="display: flex; align-items: end;">
                                        <button type="submit" class="btn-primary" style="width: 100%;">
                                            💾 ফি স্ট্রাকচার যোগ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Fee Structures List -->
                            <div>
                                <h3 style="color: #2c3e50; margin-bottom: 1rem;">📋 ফি স্ট্রাকচার তালিকা</h3>

                                <div style="overflow-x: auto;">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>ক্লাস</th>
                                                <th>ক্যাটেগরি</th>
                                                <th>পরিমাণ</th>
                                                <th>শিক্ষাবর্ষ</th>
                                                <th>শেষ তারিখ</th>
                                                <th>স্ট্যাটাস</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($fee_structures)): ?>
                                                <tr>
                                                    <td colspan="6" style="text-align: center; padding: 3rem; color: #6c757d;">
                                                        <h4>📋 কোন ফি স্ট্রাকচার পাওয়া যায়নি</h4>
                                                        <p>প্রথমে ফি স্ট্রাকচার তৈরি করুন।</p>
                                                    </td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($fee_structures as $structure): ?>
                                                    <tr>
                                                        <td><strong><?php echo htmlspecialchars($structure['class_name'] . ' - ' . $structure['section']); ?></strong></td>
                                                        <td><?php echo htmlspecialchars($structure['category_name']); ?></td>
                                                        <td><span class="amount">৳<?php echo number_format($structure['amount'], 2); ?></span></td>
                                                        <td><?php echo htmlspecialchars($structure['academic_year']); ?></td>
                                                        <td><?php echo $structure['due_date'] ? date('d/m/Y', strtotime($structure['due_date'])) : 'নির্ধারিত নয়'; ?></td>
                                                        <td>
                                                            <span class="status-badge <?php echo $structure['status'] === 'active' ? 'status-paid' : 'status-pending'; ?>">
                                                                <?php echo $structure['status'] === 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Fee Collection Tab -->
                        <div id="collection" class="tab-content">
                            <?php if (!empty($selected_class)): ?>
                                <div class="form-section">
                                    <h3 style="color: #155724; margin-bottom: 1rem;">💳 ফি সংগ্রহ করুন</h3>

                                    <form method="POST" class="form-grid">
                                        <input type="hidden" name="action" value="collect_fee">

                                        <div class="form-group">
                                            <label class="form-label">ছাত্র নির্বাচন করুন *</label>
                                            <select name="student_id" class="form-control" required>
                                                <option value="">ছাত্র নির্বাচন করুন</option>
                                                <?php foreach ($students as $student): ?>
                                                    <option value="<?php echo $student['id']; ?>">
                                                        <?php echo htmlspecialchars($student['student_name'] . ' (' . $student['student_id'] . ')'); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">ফি ক্যাটেগরি *</label>
                                            <select name="fee_structure_id" class="form-control" required>
                                                <option value="">ক্যাটেগরি নির্বাচন করুন</option>
                                                <?php foreach ($fee_structures as $structure): ?>
                                                    <option value="<?php echo $structure['id']; ?>">
                                                        <?php echo htmlspecialchars($structure['category_name'] . ' - ৳' . number_format($structure['amount'], 2)); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">প্রদত্ত পরিমাণ (টাকা) *</label>
                                            <input type="number" name="amount_paid" class="form-control" step="0.01" min="0" required placeholder="0.00">
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">পেমেন্ট পদ্ধতি *</label>
                                            <select name="payment_method" class="form-control" required>
                                                <option value="">পদ্ধতি নির্বাচন করুন</option>
                                                <option value="cash">নগদ</option>
                                                <option value="bank">ব্যাংক ট্রান্সফার</option>
                                                <option value="online">অনলাইন পেমেন্ট</option>
                                                <option value="cheque">চেক</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">রসিদ নম্বর</label>
                                            <input type="text" name="receipt_number" class="form-control" placeholder="রসিদ নম্বর">
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">মন্তব্য</label>
                                            <textarea name="remarks" class="form-control" rows="2" placeholder="অতিরিক্ত মন্তব্য"></textarea>
                                        </div>

                                        <div class="form-group" style="display: flex; align-items: end;">
                                            <button type="submit" class="btn-primary" style="width: 100%;">
                                                💰 ফি সংগ্রহ করুন
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            <?php else: ?>
                                <div style="text-align: center; padding: 3rem; color: #6c757d;">
                                    <h4>📚 ক্লাস নির্বাচন করুন</h4>
                                    <p>ফি সংগ্রহের জন্য প্রথমে একটি ক্লাস নির্বাচন করুন।</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Reports Tab -->
                        <div id="reports" class="tab-content">
                            <?php if (!empty($selected_class)): ?>
                                <h3 style="color: #2c3e50; margin-bottom: 1rem;">📊 ফি রিপোর্ট - <?php
                                    foreach ($classes as $class) {
                                        if ($class['id'] == $selected_class) {
                                            echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']);
                                            break;
                                        }
                                    }
                                ?> (<?php echo htmlspecialchars($selected_year); ?>)</h3>

                                <div style="overflow-x: auto;">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>ছাত্রের নাম</th>
                                                <th>আইডি</th>
                                                <th>ক্যাটেগরি</th>
                                                <th>মোট ফি</th>
                                                <th>প্রদত্ত</th>
                                                <th>বকেয়া</th>
                                                <th>পেমেন্ট তারিখ</th>
                                                <th>স্ট্যাটাস</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($student_fees)): ?>
                                                <tr>
                                                    <td colspan="8" style="text-align: center; padding: 3rem; color: #6c757d;">
                                                        <h4>📊 কোন ফি রেকর্ড পাওয়া যায়নি</h4>
                                                        <p>এই ক্লাসের জন্য এখনো কোন ফি সংগ্রহ করা হয়নি।</p>
                                                    </td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($student_fees as $fee): ?>
                                                    <tr>
                                                        <td><strong><?php echo htmlspecialchars($fee['student_name']); ?></strong></td>
                                                        <td><?php echo htmlspecialchars($fee['student_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($fee['category_name']); ?></td>
                                                        <td><span class="amount">৳<?php echo number_format($fee['amount_due'], 2); ?></span></td>
                                                        <td><span class="amount">৳<?php echo number_format($fee['amount_paid'], 2); ?></span></td>
                                                        <td><span class="amount-due">৳<?php echo number_format($fee['amount_due'] - $fee['amount_paid'], 2); ?></span></td>
                                                        <td><?php echo $fee['payment_date'] ? date('d/m/Y', strtotime($fee['payment_date'])) : 'পেমেন্ট নেই'; ?></td>
                                                        <td>
                                                            <span class="status-badge status-<?php echo $fee['status']; ?>">
                                                                <?php
                                                                $status_text = [
                                                                    'pending' => 'অপেক্ষমাণ',
                                                                    'partial' => 'আংশিক',
                                                                    'paid' => 'পরিশোধিত',
                                                                    'overdue' => 'বকেয়া'
                                                                ];
                                                                echo $status_text[$fee['status']] ?? $fee['status'];
                                                                ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div style="text-align: center; padding: 3rem; color: #6c757d;">
                                    <h4>📚 ক্লাস নির্বাচন করুন</h4>
                                    <p>রিপোর্ট দেখার জন্য প্রথমে একটি ক্লাস নির্বাচন করুন।</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Categories Tab -->
                        <div id="categories" class="tab-content">
                            <div class="form-section">
                                <h3 style="color: #155724; margin-bottom: 1rem;">🏷️ নতুন ফি ক্যাটেগরি যোগ করুন</h3>

                                <form method="POST" class="form-grid">
                                    <input type="hidden" name="action" value="add_category">

                                    <div class="form-group">
                                        <label class="form-label">ক্যাটেগরির নাম *</label>
                                        <input type="text" name="category_name" class="form-control" required placeholder="যেমন: মাসিক বেতন">
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">বিবরণ</label>
                                        <textarea name="description" class="form-control" rows="2" placeholder="ক্যাটেগরির বিস্তারিত বিবরণ"></textarea>
                                    </div>

                                    <div class="form-group" style="display: flex; align-items: end;">
                                        <button type="submit" class="btn-primary" style="width: 100%;">
                                            🏷️ ক্যাটেগরি যোগ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Categories List -->
                            <div>
                                <h3 style="color: #2c3e50; margin-bottom: 1rem;">🏷️ ফি ক্যাটেগরি তালিকা</h3>

                                <div style="overflow-x: auto;">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>ক্যাটেগরির নাম</th>
                                                <th>বিবরণ</th>
                                                <th>স্ট্যাটাস</th>
                                                <th>তৈরির তারিখ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($categories as $category): ?>
                                                <tr>
                                                    <td><strong><?php echo htmlspecialchars($category['category_name']); ?></strong></td>
                                                    <td><?php echo htmlspecialchars($category['description'] ?? 'বিবরণ নেই'); ?></td>
                                                    <td>
                                                        <span class="status-badge status-paid">সক্রিয়</span>
                                                    </td>
                                                    <td><?php echo date('d/m/Y', strtotime($category['created_at'] ?? 'now')); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Auto-generate receipt number
        document.addEventListener('DOMContentLoaded', function() {
            const receiptInput = document.querySelector('input[name="receipt_number"]');
            if (receiptInput && !receiptInput.value) {
                const now = new Date();
                const receiptNumber = 'RCP' + now.getFullYear() +
                                    String(now.getMonth() + 1).padStart(2, '0') +
                                    String(now.getDate()).padStart(2, '0') +
                                    String(now.getHours()).padStart(2, '0') +
                                    String(now.getMinutes()).padStart(2, '0');
                receiptInput.value = receiptNumber;
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const activeForm = document.querySelector('.tab-content.active form');
                if (activeForm) {
                    activeForm.submit();
                }
            }
        });
    </script>
</body>
</html>
