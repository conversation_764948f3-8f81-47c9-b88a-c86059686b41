<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>Classes Table Check</h2>";
    
    // Check if classes table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'classes'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>Classes table does not exist. Creating it...</p>";
        
        // Create classes table
        $sql = "CREATE TABLE classes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_name VARCHAR(50) NOT NULL,
            section VARCHAR(10),
            class_teacher_id INT,
            academic_year VARCHAR(10) NOT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>Classes table created successfully!</p>";
        
        // Insert sample classes
        $sample_classes = [
            ['Class 1', 'A', '2024'],
            ['Class 2', 'A', '2024'],
            ['Class 3', 'A', '2024'],
            ['Class 4', 'A', '2024'],
            ['Class 5', 'A', '2024'],
            ['Class 6', 'A', '2024'],
            ['Class 7', 'A', '2024'],
            ['Class 8', 'A', '2024'],
            ['Class 9', 'A', '2024'],
            ['Class 10', 'A', '2024']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year) VALUES (?, ?, ?)");
        
        foreach ($sample_classes as $class) {
            $stmt->execute($class);
        }
        
        echo "<p style='color: green;'>Sample classes inserted!</p>";
        
    } else {
        echo "<p style='color: green;'>Classes table exists.</p>";
    }
    
    // Show current classes
    $stmt = $pdo->query("SELECT * FROM classes ORDER BY class_name");
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Classes:</h3>";
    if (empty($classes)) {
        echo "<p style='color: orange;'>No classes found. Adding sample classes...</p>";
        
        // Insert sample classes
        $sample_classes = [
            ['Class 1', 'A', '2024'],
            ['Class 2', 'A', '2024'],
            ['Class 3', 'A', '2024'],
            ['Class 4', 'A', '2024'],
            ['Class 5', 'A', '2024'],
            ['Class 6', 'A', '2024'],
            ['Class 7', 'A', '2024'],
            ['Class 8', 'A', '2024'],
            ['Class 9', 'A', '2024'],
            ['Class 10', 'A', '2024']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year) VALUES (?, ?, ?)");
        
        foreach ($sample_classes as $class) {
            $stmt->execute($class);
        }
        
        echo "<p style='color: green;'>Sample classes added!</p>";
        
        // Get classes again
        $stmt = $pdo->query("SELECT * FROM classes ORDER BY class_name");
        $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Class Name</th><th>Section</th><th>Academic Year</th><th>Status</th></tr>";
    
    foreach ($classes as $class) {
        echo "<tr>";
        echo "<td>" . $class['id'] . "</td>";
        echo "<td>" . $class['class_name'] . "</td>";
        echo "<td>" . $class['section'] . "</td>";
        echo "<td>" . $class['academic_year'] . "</td>";
        echo "<td>" . $class['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check students table and update class_id if needed
    echo "<h3>Students Table Check:</h3>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'students'");
    if ($stmt->rowCount() > 0) {
        // Check if students have class_id
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM students");
        $total_students = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p>Total students: {$total_students}</p>";
        
        if ($total_students > 0) {
            // Check if students have valid class_id
            $stmt = $pdo->query("SELECT COUNT(*) as valid_class FROM students WHERE class_id IS NOT NULL AND class_id > 0");
            $valid_class_students = $stmt->fetch(PDO::FETCH_ASSOC)['valid_class'];
            
            echo "<p>Students with valid class_id: {$valid_class_students}</p>";
            
            if ($valid_class_students == 0) {
                echo "<p style='color: orange;'>Updating students with random class assignments...</p>";
                
                // Get all students
                $stmt = $pdo->query("SELECT id FROM students");
                $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Get class IDs
                $stmt = $pdo->query("SELECT id FROM classes");
                $class_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (!empty($class_ids)) {
                    $update_stmt = $pdo->prepare("UPDATE students SET class_id = ? WHERE id = ?");
                    
                    foreach ($students as $student) {
                        $random_class_id = $class_ids[array_rand($class_ids)];
                        $update_stmt->execute([$random_class_id, $student['id']]);
                    }
                    
                    echo "<p style='color: green;'>Students updated with class assignments!</p>";
                }
            }
            
            // Show students by class
            $stmt = $pdo->query("
                SELECT c.class_name, c.section, COUNT(s.id) as student_count
                FROM classes c
                LEFT JOIN students s ON c.id = s.class_id
                GROUP BY c.id, c.class_name, c.section
                ORDER BY c.class_name
            ");
            $class_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>Students by Class:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Class</th><th>Section</th><th>Student Count</th></tr>";
            
            foreach ($class_stats as $stat) {
                echo "<tr>";
                echo "<td>" . $stat['class_name'] . "</td>";
                echo "<td>" . $stat['section'] . "</td>";
                echo "<td>" . $stat['student_count'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>Students table does not exist!</p>";
    }
    
    echo "<br><h3>Setup Complete!</h3>";
    echo "<p>✅ Classes are ready for attendance system.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px;">
    <a href="student_attendance.php" style="display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">
        📋 Go to Student Attendance
    </a>
</div>
