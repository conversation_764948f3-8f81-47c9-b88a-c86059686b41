<?php
require_once '../config/database.php';
checkRole('admin');

$db = getDB();
$student_id = (int)$_GET['student_id'] ?? 0;
$message = '';
$error = '';

if (!$student_id) {
    header("Location: fees.php");
    exit;
}

// Fetch student details
$student = $db->selectOne("
    SELECT s.*, c.class_name, c.section
    FROM students s
    JOIN classes c ON s.class_id = c.id
    WHERE s.id = ?
", [$student_id]);

if (!$student) {
    header("Location: fees.php");
    exit;
}

// Fetch fee structure for the student's class
$fee_structure = $db->select("SELECT * FROM fee_structure WHERE class_id = ?", [$student['class_id']]);

// Handle fee collection
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $fee_type_id = (int)$_POST['fee_type_id'];
    $amount = (float)$_POST['amount'];
    $payment_date = $_POST['payment_date'];

    $db->insert("INSERT INTO fee_payments (student_id, fee_structure_id, amount, payment_date) VALUES (?, ?, ?, ?)", [
        $student_id,
        $fee_type_id,
        $amount,
        $payment_date
    ]);

    $message = "ফি সফলভাবে সংগ্রহ করা হয়েছে।";
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি সংগ্রহ - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">ফি সংগ্রহ</h1>
                <p class="content-subtitle">ছাত্রের ফি সংগ্রহ করুন</p>
            </div>

            <div class="content-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">ছাত্রের তথ্য</h2>
                    </div>
                    <div class="card-body">
                        <p><strong>নাম:</strong> <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></p>
                        <p><strong>ক্লাস:</strong> <?php echo htmlspecialchars($student['class_name'] . ' - ' . $student['section']); ?></p>
                        <p><strong>রোল নম্বর:</strong> <?php echo htmlspecialchars($student['roll_number']); ?></p>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h2 class="card-title">ফি সংগ্রহের فرم</h2>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST">
                            <div class="form-group">
                                <label for="fee_type_id" class="form-label">ফির ধরন</label>
                                <select id="fee_type_id" name="fee_type_id" class="form-select" required>
                                    <option value="">-- ফির ধরন নির্বাচন করুন --</option>
                                    <?php foreach ($fee_structure as $fee): ?>
                                        <option value="<?php echo $fee['id']; ?>"><?php echo htmlspecialchars($fee['fee_type'] . ' - ' . $fee['amount'] . ' টাকা'); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="amount" class="form-label">টাকার পরিমাণ</label>
                                <input type="number" id="amount" name="amount" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="payment_date" class="form-label">ফি প্রদানের তারিখ</label>
                                <input type="date" id="payment_date" name="payment_date" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <button type="submit" class="btn btn-primary">ফি জমা দিন</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../assets/js/main.js"></script>
</body>
</html>
