<?php
require_once '../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "<h2>🔧 ডাটাবেস সমস্যা সমাধান</h2>";
    
    // Step 1: Check classes table structure
    echo "<h3>Step 1: Classes Table Structure Check</h3>";
    $stmt = $pdo->query("DESCRIBE classes");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Current columns: " . implode(', ', $columns) . "</p>";
    
    // Step 2: Add academic_year column if missing
    if (!in_array('academic_year', $columns)) {
        echo "<p style='color: orange;'>⚠️ academic_year column missing. Adding now...</p>";
        
        $pdo->exec("ALTER TABLE classes ADD COLUMN academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "'");
        echo "<p style='color: green;'>✅ academic_year column added successfully!</p>";
        
        // Update existing records
        $pdo->exec("UPDATE classes SET academic_year = '" . date('Y') . "' WHERE academic_year = '' OR academic_year IS NULL");
        echo "<p style='color: green;'>✅ Existing records updated with current year!</p>";
    } else {
        echo "<p style='color: green;'>✅ academic_year column already exists!</p>";
    }
    
    // Step 3: Create class_subjects table
    echo "<h3>Step 2: Class-Subjects Table Setup</h3>";
    $sql = "CREATE TABLE IF NOT EXISTS class_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        subject_id INT NOT NULL,
        academic_year VARCHAR(10) NOT NULL DEFAULT '" . date('Y') . "',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY unique_class_subject (class_id, subject_id, academic_year)
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ class_subjects table ready!</p>";
    
    // Step 4: Add sample classes if none exist
    echo "<h3>Step 3: Sample Data Check</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM classes");
    $class_count = $stmt->fetchColumn();
    
    if ($class_count == 0) {
        echo "<p style='color: orange;'>⚠️ No classes found. Adding sample classes...</p>";
        
        $sample_classes = [
            ['প্রথম শ্রেণী', 'ক', date('Y')],
            ['দ্বিতীয় শ্রেণী', 'ক', date('Y')],
            ['তৃতীয় শ্রেণী', 'ক', date('Y')],
            ['চতুর্থ শ্রেণী', 'ক', date('Y')],
            ['পঞ্চম শ্রেণী', 'ক', date('Y')],
            ['ষষ্ঠ শ্রেণী', 'ক', date('Y')],
            ['সপ্তম শ্রেণী', 'ক', date('Y')],
            ['অষ্টম শ্রেণী', 'ক', date('Y')],
            ['নবম শ্রেণী', 'ক', date('Y')],
            ['দশম শ্রেণী', 'ক', date('Y')]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO classes (class_name, section, academic_year, status) VALUES (?, ?, ?, 'active')");
        foreach ($sample_classes as $class) {
            $stmt->execute($class);
            echo "<p style='color: green;'>✅ {$class[0]} - {$class[1]} added</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ Found {$class_count} classes in database</p>";
    }
    
    // Step 5: Test queries
    echo "<h3>Step 4: Query Testing</h3>";
    
    try {
        echo "<p><strong>Test 1:</strong> SELECT id, class_name, section, academic_year FROM classes</p>";
        $stmt = $pdo->query("SELECT id, class_name, section, academic_year FROM classes WHERE status = 'active' ORDER BY class_name, section");
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p style='color: green;'>✅ Query successful - " . count($result) . " classes found</p>";
        
        // Show first few classes
        if (!empty($result)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>ID</th><th style='padding: 8px;'>Class</th><th style='padding: 8px;'>Section</th><th style='padding: 8px;'>Year</th></tr>";
            foreach (array_slice($result, 0, 5) as $class) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$class['id']}</td>";
                echo "<td style='padding: 8px;'>{$class['class_name']}</td>";
                echo "<td style='padding: 8px;'>{$class['section']}</td>";
                echo "<td style='padding: 8px;'>{$class['academic_year']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query failed: " . $e->getMessage() . "</p>";
    }
    
    try {
        echo "<p><strong>Test 2:</strong> SELECT DISTINCT academic_year FROM classes</p>";
        $stmt = $pdo->query("SELECT DISTINCT academic_year FROM classes ORDER BY academic_year DESC");
        $years = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p style='color: green;'>✅ Query successful - Years: " . implode(', ', $years) . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query failed: " . $e->getMessage() . "</p>";
    }
    
    try {
        echo "<p><strong>Test 3:</strong> Complex JOIN query</p>";
        $stmt = $pdo->prepare("
            SELECT c.id as class_id, c.class_name, c.section, c.academic_year,
                   COUNT(cs.id) as subject_count
            FROM classes c
            LEFT JOIN class_subjects cs ON c.id = cs.class_id AND cs.academic_year = ?
            WHERE c.status = 'active'
            GROUP BY c.id, c.class_name, c.section, c.academic_year
            ORDER BY c.class_name, c.section
        ");
        $stmt->execute([date('Y')]);
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p style='color: green;'>✅ Complex query successful - " . count($result) . " results</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Complex query failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>🎉 Database Fix Complete!</h3>";
    echo "<p style='color: green; font-weight: bold;'>All database issues have been resolved. The class-subjects system should now work perfectly!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>";
}
?>

<div style="margin-top: 30px; text-align: center;">
    <a href="class_subjects_fixed.php" style="display: inline-block; padding: 15px 30px; background: #28a745; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;">
        🔗 ক্লাস-বিষয় সংযোগে যান
    </a>
    
    <a href="dashboard.php" style="display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;">
        🏠 ড্যাশবোর্ডে ফিরে যান
    </a>
</div>

<script>
// Auto redirect after 5 seconds
setTimeout(function() {
    window.location.href = 'class_subjects_fixed.php';
}, 5000);
</script>
