<?php
require_once '../config/database.php';
checkRole('admin');

$db = getDB();
$action = $_POST['action'] ?? $_GET['action'] ?? 'list';
$message = '';
$error = '';

// Fetch classes for dropdown
$classes = $db->select("SELECT * FROM classes WHERE status = 'active' ORDER BY class_name");

if ($action == 'save') {
    $class_id = (int)$_POST['class_id'];
    $fee_types = $_POST['fee_type'];
    $amounts = $_POST['amount'];

    // Clear existing fee structure for the class
    $db->delete("DELETE FROM fee_structure WHERE class_id = ?", [$class_id]);

    // Insert new fee structure
    for ($i = 0; $i < count($fee_types); $i++) {
        if (!empty($fee_types[$i]) && !empty($amounts[$i])) {
            $db->insert("INSERT INTO fee_structure (class_id, fee_type, amount) VALUES (?, ?, ?)", [
                $class_id,
                sanitize($fee_types[$i]),
                (float)$amounts[$i]
            ]);
        }
    }
    $message = "ফি কাঠামো সফলভাবে সংরক্ষণ করা হয়েছে।";
}

// Fetch fee structure for a selected class
$selected_class_id = $_GET['class_id'] ?? ($classes[0]['id'] ?? null);
$fee_structure = [];
if ($selected_class_id) {
    $fee_structure = $db->select("SELECT * FROM fee_structure WHERE class_id = ? ORDER BY fee_type", [$selected_class_id]);
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি কাঠামো - স্কুল ব্যবস্থাপনা সিস্টেম</title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include '../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-header">
                <h1 class="content-title">ফি কাঠামো</h1>
                <p class="content-subtitle">ক্লাস অনুযায়ী ফি কাঠামো নির্ধারণ করুন</p>
            </div>

            <div class="content-body">
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">ক্লাস নির্বাচন করুন</h2>
                    </div>
                    <div class="card-body">
                        <form action="" method="GET">
                            <div class="form-group">
                                <label for="class_id" class="form-label">ক্লাস</label>
                                <select id="class_id" name="class_id" class="form-select" onchange="this.form.submit()">
                                    <?php foreach ($classes as $class): ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($selected_class_id == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['section']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h2 class="card-title">ফি কাঠামো সেট করুন</h2>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST">
                            <input type="hidden" name="action" value="save">
                            <input type="hidden" name="class_id" value="<?php echo $selected_class_id; ?>">
                            <div id="fee-structure-container">
                                <?php if (!empty($fee_structure)): ?>
                                    <?php foreach ($fee_structure as $fee): ?>
                                        <div class="fee-structure-row">
                                            <input type="text" name="fee_type[]" class="form-control" placeholder="ফির ধরন" value="<?php echo htmlspecialchars($fee['fee_type']); ?>" required>
                                            <input type="number" name="amount[]" class="form-control" placeholder="টাকার পরিমাণ" value="<?php echo htmlspecialchars($fee['amount']); ?>" required>
                                            <button type="button" class="btn btn-danger btn-sm remove-row">মুছুন</button>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="fee-structure-row">
                                        <input type="text" name="fee_type[]" class="form-control" placeholder="ফির ধরন" required>
                                        <input type="number" name="amount[]" class="form-control" placeholder="টাকার পরিমাণ" required>
                                        <button type="button" class="btn btn-danger btn-sm remove-row">মুছুন</button>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <button type="button" id="add-row" class="btn btn-secondary mt-2">নতুন সারি যোগ করুন</button>
                            <button type="submit" class="btn btn-primary mt-2">সংরক্ষণ করুন</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../assets/js/main.js"></script>
    <script>
        document.getElementById('add-row').addEventListener('click', function() {
            const container = document.getElementById('fee-structure-container');
            const row = document.createElement('div');
            row.className = 'fee-structure-row';
            row.innerHTML = `
                <input type="text" name="fee_type[]" class="form-control" placeholder="ফির ধরন" required>
                <input type="number" name="amount[]" class="form-control" placeholder="টাকার পরিমাণ" required>
                <button type="button" class="btn btn-danger btn-sm remove-row">মুছুন</button>
            `;
            container.appendChild(row);
        });

        document.getElementById('fee-structure-container').addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-row')) {
                e.target.closest('.fee-structure-row').remove();
            }
        });
    </script>
    <style>
        .fee-structure-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
    </style>
</body>
</html>
