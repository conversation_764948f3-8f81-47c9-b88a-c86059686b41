-- স্কুল ব্যবস্থাপনা সিস্টেম ডাটাবেস
-- School Management System Database

CREATE DATABASE IF NOT EXISTS school_management;
USE school_management;

-- ব্যবহার<PERSON><PERSON><PERSON><PERSON> টেবিল (Users Table)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role ENUM('admin', 'teacher', 'student') NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ক<PERSON><PERSON>া<PERSON> টেবিল (Classes Table)
CREATE TABLE classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL,
    section VARCHAR(10),
    class_teacher_id INT,
    academic_year VARCHAR(10) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (class_teacher_id) REFERENCES users(id) ON DELETE SET NULL
);

-- বিষয় টেবিল (Subjects Table)
CREATE TABLE subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ক্লাস-বিষয় সম্পর্ক টেবিল (Class-Subject Relationship)
CREATE TABLE class_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    teacher_id INT,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_class_subject (class_id, subject_id)
);

-- শিক্ষক তথ্য টেবিল (Teachers Table)
CREATE TABLE teachers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(15),
    address TEXT,
    qualification VARCHAR(200),
    joining_date DATE,
    salary DECIMAL(10,2),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ছাত্র তথ্য টেবিল (Students Table)
CREATE TABLE students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE NOT NULL,
    student_id VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    class_id INT NOT NULL,
    roll_number INT NOT NULL,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    photo VARCHAR(255),
    father_name VARCHAR(100),
    mother_name VARCHAR(100),
    guardian_name VARCHAR(100),
    guardian_relation VARCHAR(50),
    guardian_phone VARCHAR(15),
    guardian_email VARCHAR(100),
    guardian_address TEXT,
    phone VARCHAR(15),
    parent_phone VARCHAR(15),
    address TEXT,
    admission_date DATE,
    status ENUM('active', 'inactive', 'graduated') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_class_roll (class_id, roll_number)
);

-- উপস্থিতি টেবিল (Attendance Table)
CREATE TABLE attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    status ENUM('present', 'absent', 'late') NOT NULL,
    remarks TEXT,
    marked_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_attendance (student_id, class_id, subject_id, attendance_date)
);

-- পরীক্ষা টেবিল (Exams Table)
CREATE TABLE exams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exam_name VARCHAR(100) NOT NULL,
    exam_type ENUM('midterm', 'final', 'quiz', 'assignment') NOT NULL,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    exam_date DATE NOT NULL,
    total_marks INT NOT NULL,
    pass_marks INT NOT NULL,
    duration_minutes INT,
    status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
);

-- নম্বর টেবিল (Marks Table)
CREATE TABLE marks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    exam_id INT NOT NULL,
    obtained_marks DECIMAL(5,2) NOT NULL,
    remarks TEXT,
    entered_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (entered_by) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_student_exam (student_id, exam_id)
);

-- নোটিশ টেবিল (Notices Table)
CREATE TABLE notices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    target_audience ENUM('all', 'teachers', 'students', 'specific_class') NOT NULL,
    class_id INT NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    published_by INT NOT NULL,
    publish_date DATE NOT NULL,
    expiry_date DATE,
    status ENUM('draft', 'published', 'expired') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (published_by) REFERENCES users(id) ON DELETE RESTRICT
);

-- ডিফল্ট অ্যাডমিন ইউজার তৈরি (পাসওয়ার্ড: password)
INSERT INTO users (username, password, email, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'admin');

-- নমুনা ক্লাস
INSERT INTO classes (class_name, section, academic_year) VALUES
('Class 1', 'A', '2024'),
('Class 2', 'A', '2024'),
('Class 3', 'A', '2024'),
('Class 4', 'A', '2024'),
('Class 5', 'A', '2024');

-- নমুনা বিষয়
INSERT INTO subjects (subject_name, subject_code) VALUES
('বাংলা', 'BAN101'),
('ইংরেজি', 'ENG101'),
('গণিত', 'MAT101'),
('বিজ্ঞান', 'SCI101'),
('সমাজ', 'SOC101');

-- নমুনা শিক্ষক ইউজার (পাসওয়ার্ড: password)
INSERT INTO users (username, password, email, role) VALUES
('teacher1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'teacher'),
('teacher2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'teacher');

-- নমুনা শিক্ষক তথ্য
INSERT INTO teachers (user_id, employee_id, first_name, last_name, phone, qualification, joining_date, salary) VALUES
(2, 'T001', 'মোহাম্মদ', 'রহিম', '01711111111', 'এমএ (বাংলা)', '2024-01-01', 25000.00),
(3, 'T002', 'ফাতেমা', 'খাতুন', '01722222222', 'এমএ (ইংরেজি)', '2024-01-01', 25000.00);

-- নমুনা ছাত্র ইউজার (পাসওয়ার্ড: password)
INSERT INTO users (username, password, email, role) VALUES
('student1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'student'),
('student2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'student'),
('student3', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'student');

-- নমুনা ছাত্র তথ্য
INSERT INTO students (user_id, student_id, first_name, last_name, class_id, roll_number, date_of_birth, gender, father_name, mother_name, guardian_name, guardian_relation, guardian_phone, guardian_email, phone, parent_phone, address, admission_date) VALUES
(4, 'S001', 'আহমেদ', 'আলী', 1, 1, '2010-05-15', 'male', 'মোহাম্মদ আলী', 'ফাতেমা খাতুন', 'মোহাম্মদ আলী', 'পিতা', '01744444444', '<EMAIL>', '01733333333', '01744444444', 'ঢাকা, বাংলাদেশ', '2024-01-01'),
(5, 'S002', 'ফাতিমা', 'বেগম', 1, 2, '2010-08-20', 'female', 'আব্দুল করিম', 'রাহেলা বেগম', 'আব্দুল করিম', 'পিতা', '01766666666', '<EMAIL>', '01755555555', '01766666666', 'চট্টগ্রাম, বাংলাদেশ', '2024-01-01'),
(6, 'S003', 'মোহাম্মদ', 'হাসান', 2, 1, '2009-12-10', 'male', 'আহমেদ হাসান', 'সালমা খাতুন', 'আহমেদ হাসান', 'পিতা', '01788888888', '<EMAIL>', '01777777777', '01788888888', 'সিলেট, বাংলাদেশ', '2024-01-01');

-- ক্লাস-বিষয় সম্পর্ক
INSERT INTO class_subjects (class_id, subject_id, teacher_id) VALUES
(1, 1, 2), -- Class 1 - বাংলা - Teacher 1
(1, 2, 3), -- Class 1 - ইংরেজি - Teacher 2
(1, 3, 2), -- Class 1 - গণিত - Teacher 1
(2, 1, 2), -- Class 2 - বাংলা - Teacher 1
(2, 2, 3), -- Class 2 - ইংরেজি - Teacher 2
(2, 3, 2); -- Class 2 - গণিত - Teacher 1

-- নমুনা নোটিশ
INSERT INTO notices (title, content, target_audience, priority, published_by, publish_date, status) VALUES
('স্কুল খোলার নোটিশ', 'আগামী সোমবার থেকে স্কুল খোলা থাকবে। সকল ছাত্র-ছাত্রী নিয়মিত উপস্থিত থাকবেন।', 'all', 'high', 1, CURDATE(), 'published'),
('পরীক্ষার সময়সূচী', 'আগামী মাসে অর্ধবার্ষিক পরীক্ষা অনুষ্ঠিত হবে। বিস্তারিত শীঘ্রই জানানো হবে।', 'students', 'medium', 1, CURDATE(), 'published');
