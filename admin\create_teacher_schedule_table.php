<?php
require_once '../config/database.php';

$db = new Database();
$pdo = $db->getConnection();

try {
    // Create teacher_schedules table
    $sql = "CREATE TABLE IF NOT EXISTS teacher_schedules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT NOT NULL,
        subject_id INT NOT NULL,
        class_id INT NOT NULL,
        day_of_week ENUM('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday') NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        room_number VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        <PERSON>OREIG<PERSON> KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
        INDEX idx_teacher_day (teacher_id, day_of_week),
        INDEX idx_class_day (class_id, day_of_week),
        INDEX idx_time_range (start_time, end_time)
    )";
    
    $pdo->exec($sql);
    echo "✅ teacher_schedules টেবিল সফলভাবে তৈরি হয়েছে!<br>";
    
    // Check if teachers table exists, if not create it
    $stmt = $pdo->query("SHOW TABLES LIKE 'teachers'");
    if ($stmt->rowCount() == 0) {
        $sql_teachers = "CREATE TABLE IF NOT EXISTS teachers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            teacher_id VARCHAR(20) UNIQUE NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) UNIQUE,
            phone VARCHAR(20),
            address TEXT,
            qualification VARCHAR(100),
            subject_specialization VARCHAR(100),
            joining_date DATE,
            salary DECIMAL(10,2),
            status ENUM('active', 'inactive') DEFAULT 'active',
            photo VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql_teachers);
        echo "✅ teachers টেবিল সফলভাবে তৈরি হয়েছে!<br>";
        
        // Insert sample teachers
        $sample_teachers = [
            ['TCH001', 'মোহাম্মদ', 'রহিম', '<EMAIL>', '01711111111', 'ঢাকা', 'এমএ (বাংলা)', 'বাংলা', '2020-01-15', 25000],
            ['TCH002', 'ফাতেমা', 'খাতুন', '<EMAIL>', '01722222222', 'চট্টগ্রাম', 'এমএসসি (গণিত)', 'গণিত', '2019-03-10', 28000],
            ['TCH003', 'আব্দুল', 'করিম', '<EMAIL>', '01733333333', 'সিলেট', 'এমএ (ইংরেজি)', 'ইংরেজি', '2021-06-20', 26000],
            ['TCH004', 'রাশিদা', 'বেগম', '<EMAIL>', '01744444444', 'রাজশাহী', 'এমএসসি (পদার্থবিজ্ঞান)', 'বিজ্ঞান', '2018-09-05', 30000],
            ['TCH005', 'নাসির', 'উদ্দিন', '<EMAIL>', '01755555555', 'খুলনা', 'এমএ (ইতিহাস)', 'সামাজিক বিজ্ঞান', '2020-11-12', 24000]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, address, qualification, subject_specialization, joining_date, salary) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($sample_teachers as $teacher) {
            $stmt->execute($teacher);
        }
        
        echo "✅ নমুনা শিক্ষক ডেটা যোগ করা হয়েছে!<br>";
    }
    
    // Check if subjects table exists, if not create it
    $stmt = $pdo->query("SHOW TABLES LIKE 'subjects'");
    if ($stmt->rowCount() == 0) {
        $sql_subjects = "CREATE TABLE IF NOT EXISTS subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subject_name VARCHAR(100) NOT NULL,
            subject_code VARCHAR(20) UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql_subjects);
        echo "✅ subjects টেবিল সফলভাবে তৈরি হয়েছে!<br>";
        
        // Insert sample subjects
        $sample_subjects = [
            ['বাংলা', 'BAN101', 'বাংলা ভাষা ও সাহিত্য'],
            ['ইংরেজি', 'ENG101', 'ইংরেজি ভাষা ও সাহিত্য'],
            ['গণিত', 'MAT101', 'গণিত'],
            ['পদার্থবিজ্ঞান', 'PHY101', 'পদার্থবিজ্ঞান'],
            ['রসায়ন', 'CHE101', 'রসায়ন'],
            ['জীববিজ্ঞান', 'BIO101', 'জীববিজ্ঞান'],
            ['ইতিহাস', 'HIS101', 'ইতিহাস ও ঐতিহ্য'],
            ['ভূগোল', 'GEO101', 'ভূগোল ও পরিবেশ'],
            ['ইসলামিক স্টাডিজ', 'ISL101', 'ইসলামিক স্টাডিজ'],
            ['শারীরিক শিক্ষা', 'PE101', 'শারীরিক শিক্ষা ও স্বাস্থ্য']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO subjects (subject_name, subject_code, description) VALUES (?, ?, ?)");
        
        foreach ($sample_subjects as $subject) {
            $stmt->execute($subject);
        }
        
        echo "✅ নমুনা বিষয় ডেটা যোগ করা হয়েছে!<br>";
    }
    
    // Insert some sample schedules
    $sample_schedules = [
        [1, 1, 1, 'sunday', '08:00:00', '08:45:00', '১০১'],
        [1, 1, 1, 'monday', '08:00:00', '08:45:00', '১০১'],
        [1, 1, 1, 'tuesday', '08:00:00', '08:45:00', '১০১'],
        [2, 3, 2, 'sunday', '09:00:00', '09:45:00', '২০২'],
        [2, 3, 2, 'monday', '09:00:00', '09:45:00', '২০২'],
        [3, 2, 1, 'wednesday', '10:00:00', '10:45:00', '১০৩'],
        [3, 2, 2, 'thursday', '10:00:00', '10:45:00', '২০৩'],
        [4, 4, 3, 'sunday', '11:00:00', '11:45:00', 'ল্যাব-১'],
        [4, 5, 3, 'tuesday', '11:00:00', '11:45:00', 'ল্যাব-২'],
        [5, 7, 1, 'friday', '02:00:00', '02:45:00', '১০৫']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO teacher_schedules (teacher_id, subject_id, class_id, day_of_week, start_time, end_time, room_number) VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    foreach ($sample_schedules as $schedule) {
        try {
            $stmt->execute($schedule);
        } catch (Exception $e) {
            // Skip if already exists or foreign key constraint fails
        }
    }
    
    echo "✅ নমুনা সময়সূচী ডেটা যোগ করা হয়েছে!<br>";
    
    echo "<br><h3>✅ সব টেবিল এবং ডেটা সফলভাবে তৈরি হয়েছে!</h3>";
    echo "<p><a href='teacher_schedule.php'>শিক্ষক সময়সূচী পেজে যান</a></p>";
    
} catch (PDOException $e) {
    echo "❌ ডাটাবেস এরর: " . $e->getMessage();
}
?>
