# স্কুল ব্যবস্থাপনা সিস্টেম (School Management System)

একটি সম্পূর্ণ স্কুল ব্যবস্থাপনা সিস্টেম যা PHP, MySQL, CSS, JavaScript এবং AJAX ব্যবহার করে তৈরি।

## বৈশিষ্ট্যসমূহ

### অ্যাডমিন প্যানেল
- ড্যাশবোর্ড সহ সম্পূর্ণ ওভারভিউ
- ছাত্র ব্যবস্থাপনা (যোগ, সম্পাদনা, মুছে ফেলা)
- শিক্ষক ব্যবস্থাপনা
- ক্লাস ও বিষয় ব্যবস্থাপনা
- উপস্থিতি ট্র্যাকিং
- নম্বর ব্যবস্থাপনা
- নোটিশ বোর্ড

### শিক্ষক প্যানেল
- নিজস্ব ড্যাশবোর্ড
- ছাত্রদের উপস্থিতি নেওয়া
- নম্বর এন্ট্রি
- ক্লাস ও বিষয়ের তথ্য দেখা

### ছাত্র প্যানেল
- ব্যক্তিগত ড্যাশবোর্ড
- নিজের নম্বর দেখা
- উপস্থিতির রেকর্ড
- নোটিশ দেখা

## প্রযুক্তি স্ট্যাক

- **Backend:** PHP 7.4+
- **Database:** MySQL 5.7+
- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **AJAX:** Fetch API
- **Styling:** Custom CSS with Responsive Design

## ইনস্টলেশন নির্দেশনা

### প্রয়োজনীয়তা
- XAMPP/WAMP/LAMP সার্ভার
- PHP 7.4 বা তার উপরে
- MySQL 5.7 বা তার উপরে
- Apache Web Server

### ধাপসমূহ

1. **প্রজেক্ট ডাউনলোড করুন**
   ```bash
   git clone [repository-url]
   cd skool
   ```

2. **XAMPP/WAMP চালু করুন**
   - Apache এবং MySQL সার্ভিস চালু করুন

3. **ডাটাবেস তৈরি করুন**
   - phpMyAdmin খুলুন (http://localhost/phpmyadmin)
   - `school_management` নামে একটি নতুন ডাটাবেস তৈরি করুন
   - `database/school_management.sql` ফাইলটি ইমপোর্ট করুন

4. **ডাটাবেস কনফিগারেশন**
   - `config/database.php` ফাইল খুলুন
   - প্রয়োজনে ডাটাবেস সেটিংস পরিবর্তন করুন:
     ```php
     define('DB_HOST', 'localhost');
     define('DB_USERNAME', 'root');
     define('DB_PASSWORD', '');
     define('DB_NAME', 'school_management');
     ```

5. **প্রজেক্ট চালু করুন**
   - ব্রাউজারে যান: http://localhost/skool

## ডিফল্ট লগইন তথ্য

### অ্যাডমিন
- **ইউজারনেম:** admin
- **পাসওয়ার্ড:** password

### শিক্ষক
- **ইউজারনেম:** teacher1
- **পাসওয়ার্ড:** password

### ছাত্র
- **ইউজারনেম:** student1
- **পাসওয়ার্ড:** password

## ফোল্ডার স্ট্রাকচার

```
skool/
├── admin/                 # অ্যাডমিন প্যানেল ফাইলসমূহ
│   ├── dashboard.php
│   ├── students.php
│   ├── teachers.php
│   └── ...
├── teacher/               # শিক্ষক প্যানেল ফাইলসমূহ
├── student/               # ছাত্র প্যানেল ফাইলসমূহ
├── assets/                # CSS, JS এবং অন্যান্য সম্পদ
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
├── config/                # কনফিগারেশন ফাইলসমূহ
│   └── database.php
├── database/              # ডাটাবেস ফাইলসমূহ
│   └── school_management.sql
├── index.php              # মূল ইনডেক্স ফাইল
├── login.php              # লগইন পেজ
├── logout.php             # লগআউট ফাইল
└── README.md              # এই ফাইল
```

## ডাটাবেস স্ট্রাকচার

### মূল টেবিলসমূহ
- `users` - সকল ব্যবহারকারীর তথ্য
- `students` - ছাত্রদের বিস্তারিত তথ্য
- `teachers` - শিক্ষকদের বিস্তারিত তথ্য
- `classes` - ক্লাসের তথ্য
- `subjects` - বিষয়ের তথ্য
- `attendance` - উপস্থিতির রেকর্ড
- `marks` - পরীক্ষার নম্বর
- `exams` - পরীক্ষার তথ্য
- `notices` - নোটিশ বোর্ড

## বৈশিষ্ট্য বিবরণ

### AJAX ফিচার
- রিয়েল-টাইম ডেটা লোডিং
- পেজ রিফ্রেশ ছাড়াই ফর্ম সাবমিশন
- ডায়নামিক কন্টেন্ট আপডেট

### রেসপন্সিভ ডিজাইন
- মোবাইল-ফ্রেন্ডলি ইন্টারফেস
- ট্যাবলেট এবং ডেস্কটপ সাপোর্ট
- ক্রস-ব্রাউজার কম্প্যাটিবিলিটি

### নিরাপত্তা
- SQL ইনজেকশন প্রতিরোধ
- XSS প্রতিরোধ
- সেশন ম্যানেজমেন্ট
- পাসওয়ার্ড হ্যাশিং

## ভবিষ্যত উন্নতি

- [ ] ইমেইল নোটিফিকেশন
- [ ] SMS সার্ভিস
- [ ] অনলাইন পেমেন্ট
- [ ] রিপোর্ট জেনারেশন (PDF)
- [ ] ক্যালেন্ডার ইন্টিগ্রেশন
- [ ] ফাইল আপলোড সিস্টেম

## সাপোর্ট

কোন সমস্যা বা প্রশ্ন থাকলে ইস্যু তৈরি করুন অথবা যোগাযোগ করুন।

## লাইসেন্স

এই প্রজেক্টটি MIT লাইসেন্সের অধীনে প্রকাশিত।
